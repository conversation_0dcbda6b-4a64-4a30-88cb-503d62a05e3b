#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全面测试物资管理系统的所有路由
"""

import requests
import time
from datetime import datetime

BASE_URL = "http://127.0.0.1:5000"

def test_route(session, method, url, data=None, description=""):
    """测试单个路由"""
    try:
        if method.upper() == 'GET':
            response = session.get(url)
        elif method.upper() == 'POST':
            response = session.post(url, data=data)
        
        if response.status_code == 200:
            print(f"✅ {description}")
            return True
        else:
            print(f"❌ {description} (状态码: {response.status_code})")
            return False
    except Exception as e:
        print(f"❌ {description} (错误: {str(e)})")
        return False

def login_user(session, username, password):
    """用户登录"""
    login_data = {
        'username': username,
        'password': password
    }
    response = session.post(f"{BASE_URL}/auth/login", data=login_data)
    return response.status_code == 200 or 'dashboard' in response.url

def main():
    print("物资管理系统全面路由测试")
    print("测试时间:", datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
    print("=" * 60)
    
    # 检查服务器是否运行
    try:
        response = requests.get(BASE_URL, timeout=5)
        print("✅ 服务器运行正常")
    except:
        print("❌ 服务器未运行，请先启动服务器")
        return
    
    print("=" * 60)
    print("开始全面路由测试")
    print("=" * 60)
    
    # 测试管理员功能
    print("\n--- 测试管理员功能 ---")
    admin_session = requests.Session()
    
    if login_user(admin_session, 'admin', 'admin123'):
        print("✅ 管理员登录成功")
        
        # 基础页面测试
        test_route(admin_session, 'GET', f"{BASE_URL}/dashboard", description="仪表板访问")
        test_route(admin_session, 'GET', f"{BASE_URL}/materials", description="物资列表访问")
        test_route(admin_session, 'GET', f"{BASE_URL}/reports", description="报表仪表板访问")
        
        # 新增路由测试
        test_route(admin_session, 'GET', f"{BASE_URL}/requests", description="申请审核页面访问")
        test_route(admin_session, 'GET', f"{BASE_URL}/allocations", description="分配记录页面访问")
        test_route(admin_session, 'GET', f"{BASE_URL}/reports/materials", description="物资报表页面访问")
        test_route(admin_session, 'GET', f"{BASE_URL}/reports/departments", description="科室报表页面访问")
        test_route(admin_session, 'GET', f"{BASE_URL}/auth/profile", description="个人信息页面访问")
        
        # 管理员专用功能
        test_route(admin_session, 'GET', f"{BASE_URL}/materials/add", description="添加物资页面访问")
        
        print("✅ 管理员登出")
        admin_session.get(f"{BASE_URL}/auth/logout")
    else:
        print("❌ 管理员登录失败")
    
    # 测试员工功能
    print("\n--- 测试员工功能 ---")
    employee_session = requests.Session()
    
    if login_user(employee_session, 'zhangsan', 'password123'):
        print("✅ 员工登录成功")
        
        # 基础页面测试
        test_route(employee_session, 'GET', f"{BASE_URL}/dashboard", description="仪表板访问")
        test_route(employee_session, 'GET', f"{BASE_URL}/materials", description="物资列表访问")
        test_route(employee_session, 'GET', f"{BASE_URL}/reports", description="报表仪表板访问")
        
        # 员工可访问的路由
        test_route(employee_session, 'GET', f"{BASE_URL}/allocations", description="分配记录页面访问")
        test_route(employee_session, 'GET', f"{BASE_URL}/reports/materials", description="物资报表页面访问")
        test_route(employee_session, 'GET', f"{BASE_URL}/reports/departments", description="科室报表页面访问")
        test_route(employee_session, 'GET', f"{BASE_URL}/auth/profile", description="个人信息页面访问")
        
        # 测试权限限制（员工不能访问的页面）
        print("\n--- 测试员工权限限制 ---")
        response = employee_session.get(f"{BASE_URL}/requests")
        if response.status_code == 403 or 'login' in response.url or '需要管理员权限' in response.text:
            print("✅ 申请审核页面权限限制正常")
        else:
            print("❌ 申请审核页面权限限制异常")
            
        response = employee_session.get(f"{BASE_URL}/materials/add")
        if response.status_code == 403 or 'login' in response.url or '需要管理员权限' in response.text:
            print("✅ 添加物资页面权限限制正常")
        else:
            print("❌ 添加物资页面权限限制异常")
        
        print("✅ 员工登出")
        employee_session.get(f"{BASE_URL}/auth/logout")
    else:
        print("❌ 员工登录失败")
    
    # 测试未登录访问
    print("\n--- 测试未登录访问限制 ---")
    guest_session = requests.Session()
    
    protected_routes = [
        "/dashboard",
        "/materials",
        "/reports",
        "/requests",
        "/allocations",
        "/auth/profile"
    ]
    
    for route in protected_routes:
        response = guest_session.get(f"{BASE_URL}{route}")
        if 'login' in response.url or response.status_code == 302:
            print(f"✅ {route} 未登录访问限制正常")
        else:
            print(f"❌ {route} 未登录访问限制异常")
    
    print("\n" + "=" * 60)
    print("全面路由测试完成")
    print("=" * 60)

if __name__ == "__main__":
    main()
