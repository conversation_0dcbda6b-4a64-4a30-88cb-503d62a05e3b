#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
金融企业物资管理系统启动脚本
"""

import os
import sys
import subprocess
import time
import pymysql
from config import Config

def check_database():
    """检查数据库连接"""
    try:
        conn = pymysql.connect(**Config.DB_CONFIG)
        conn.close()
        return True
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return False

def start_system():
    """启动系统"""
    print("=" * 60)
    print("🏦 金融企业物资管理系统")
    print("=" * 60)
    
    # 检查数据库连接
    print("🔍 检查数据库连接...")
    if not check_database():
        print("❌ 数据库连接失败，请检查MySQL服务是否启动")
        print("   数据库配置:")
        print(f"   - 主机: {Config.DB_CONFIG['host']}")
        print(f"   - 用户: {Config.DB_CONFIG['user']}")
        print(f"   - 数据库: {Config.DB_CONFIG['database']}")
        return False
    
    print("✅ 数据库连接正常")
    
    # 启动Flask应用
    print("🚀 启动Web应用...")
    print("📍 访问地址: http://localhost:5000")
    print("👤 登录账号:")
    print("   - 管理员: admin / 123456")
    print("   - 员工: zhangsan / 123456")
    print("=" * 60)
    print("💡 按 Ctrl+C 停止服务")
    print("=" * 60)
    
    try:
        # 启动Flask应用
        from app import create_app
        app = create_app()
        app.run(debug=True, host='0.0.0.0', port=5000)
    except KeyboardInterrupt:
        print("\n👋 系统已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")

if __name__ == '__main__':
    start_system()
