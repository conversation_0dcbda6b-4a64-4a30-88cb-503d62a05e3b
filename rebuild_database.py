#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pymysql
from datetime import datetime, date
import hashlib

def hash_password(password):
    """密码哈希"""
    return hashlib.md5(password.encode()).hexdigest()

def rebuild_database():
    """重建数据库"""
    # 数据库连接配置
    config = {
        'host': 'localhost',
        'user': 'root',
        'password': 'qyf20031211',
        'database': 'goods',
        'charset': 'utf8mb4'
    }
    
    try:
        # 连接数据库
        connection = pymysql.connect(**config)
        cursor = connection.cursor()
        
        print("删除现有表...")
        # 删除表（注意外键约束的顺序）
        cursor.execute("DROP TABLE IF EXISTS material_requests")
        cursor.execute("DROP TABLE IF EXISTS material_allocations")
        cursor.execute("DROP TABLE IF EXISTS materials")
        cursor.execute("DROP TABLE IF EXISTS users")
        cursor.execute("DROP TABLE IF EXISTS departments")
        
        print("重新创建表...")
        
        # 创建科室表
        cursor.execute("""
        CREATE TABLE departments (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(100) NOT NULL UNIQUE,
            description TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )
        """)
        
        # 创建用户表
        cursor.execute("""
        CREATE TABLE users (
            id INT AUTO_INCREMENT PRIMARY KEY,
            username VARCHAR(50) NOT NULL UNIQUE,
            password VARCHAR(255) NOT NULL,
            real_name VARCHAR(100) NOT NULL,
            role ENUM('admin', 'employee') DEFAULT 'employee',
            department_id INT NULL,
            email VARCHAR(100),
            phone VARCHAR(20),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (department_id) REFERENCES departments(id)
        )
        """)
        
        # 创建物资表
        cursor.execute("""
        CREATE TABLE materials (
            id INT AUTO_INCREMENT PRIMARY KEY,
            asset_number VARCHAR(50) UNIQUE,
            name VARCHAR(200) NOT NULL,
            model VARCHAR(100),
            category ENUM('fixed_asset', 'consumable') NOT NULL,
            price DECIMAL(10,2) NOT NULL,
            purchase_date DATE NOT NULL,
            supplier VARCHAR(200),
            purchase_amount DECIMAL(10,2),
            quantity INT DEFAULT 1,
            remaining_quantity INT DEFAULT 1,
            status ENUM('in_use', 'scrapped', 'available') DEFAULT 'available',
            scrap_reason TEXT,
            scrap_date DATE,
            description TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )
        """)
        
        # 创建物资分配表
        cursor.execute("""
        CREATE TABLE material_allocations (
            id INT AUTO_INCREMENT PRIMARY KEY,
            material_id INT NOT NULL,
            department_id INT NOT NULL,
            user_id INT,
            allocated_by INT NOT NULL,
            quantity INT DEFAULT 1,
            allocation_date DATE NOT NULL,
            return_date DATE,
            status ENUM('allocated', 'returned', 'consumed') DEFAULT 'allocated',
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (material_id) REFERENCES materials(id),
            FOREIGN KEY (department_id) REFERENCES departments(id),
            FOREIGN KEY (user_id) REFERENCES users(id),
            FOREIGN KEY (allocated_by) REFERENCES users(id)
        )
        """)
        
        # 创建物资申请表（包含department_id字段）
        cursor.execute("""
        CREATE TABLE material_requests (
            id INT AUTO_INCREMENT PRIMARY KEY,
            material_id INT NOT NULL,
            user_id INT NOT NULL,
            department_id INT NOT NULL,
            quantity INT NOT NULL DEFAULT 1,
            reason TEXT,
            status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
            request_date DATE NOT NULL,
            processed_date DATE,
            processed_by INT,
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (material_id) REFERENCES materials(id),
            FOREIGN KEY (user_id) REFERENCES users(id),
            FOREIGN KEY (department_id) REFERENCES departments(id),
            FOREIGN KEY (processed_by) REFERENCES users(id),
            INDEX idx_material_id (material_id),
            INDEX idx_user_id (user_id),
            INDEX idx_status (status)
        )
        """)
        
        connection.commit()
        print("✅ 数据库表重建完成！")
        
        cursor.close()
        connection.close()
        
    except Exception as e:
        print(f"❌ 重建数据库失败: {e}")
        raise

if __name__ == "__main__":
    rebuild_database()
    print("现在运行 python init_database.py 来插入示例数据")
