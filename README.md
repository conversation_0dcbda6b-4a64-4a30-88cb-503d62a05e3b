# 金融企业物资管理系统

## 系统简介
这是一个基于Flask的金融企业物资管理系统，支持物资申请、审批、分配等功能。

## 环境要求
- Python 3.8+
- MySQL 5.7+
- 相关Python包（见requirements.txt）

## 数据库配置
系统已配置连接到您的MySQL数据库：
- 主机: localhost
- 用户: root
- 密码: lax217652
- 数据库: goods

## 快速启动

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 初始化数据库（首次运行）
```bash
python init_database.py
```

### 3. 启动系统
```bash
python start_system.py
```
或者直接运行：
```bash
python app.py
```

### 4. 访问系统
打开浏览器访问：http://localhost:5000

## 登录账号
- **管理员账号**: admin / 123456
- **普通员工**: zhangsan / 123456
- **其他员工**: lisi, wangwu, zhaoliu / 123456

## 系统功能

### 管理员功能
- 物资管理（添加、编辑、删除）
- 申请审批
- 物资分配
- 统计报表
- 用户管理

### 员工功能
- 物资申请
- 查看申请状态
- 个人物资记录
- 基础统计

## 文件说明
- `app.py` - Flask应用主文件
- `config.py` - 数据库配置
- `init_database.py` - 数据库初始化脚本
- `start_system.py` - 系统启动脚本
- `test_system_connection.py` - 连接测试脚本
- `requirements.txt` - Python依赖包

## 目录结构
```
├── controllers/     # 控制器
├── dao/            # 数据访问层
├── models/         # 数据模型
├── services/       # 业务逻辑层
├── templates/      # HTML模板
├── static/         # 静态文件
└── tests/          # 测试文件
```

## 故障排除

### 数据库连接失败
1. 确认MySQL服务已启动
2. 检查用户名密码是否正确
3. 确认数据库'goods'已创建

### 端口占用
如果5000端口被占用，可以修改app.py中的端口号

### 依赖包问题
```bash
pip install --upgrade pip
pip install -r requirements.txt
```

## 技术栈
- **后端**: Flask + PyMySQL
- **前端**: Bootstrap + jQuery
- **数据库**: MySQL
- **报表**: ReportLab + openpyxl
