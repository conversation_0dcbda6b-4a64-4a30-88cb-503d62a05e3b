#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能库存管理Agent
监控库存水平、预测补货需求、优化分配策略
"""

import json
import math
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from agents.base_agent import BaseAgent, AgentResponse, AgentError
from dao.material_dao import MaterialDAO
from dao.allocation_dao import AllocationDAO, RequestDAO
from dao.user_dao import UserDAO
from dao.department_dao import DepartmentDAO

class InventoryManagerAgent(BaseAgent):
    """智能库存管理助手"""
    
    def __init__(self):
        super().__init__(
            agent_id="inventory_manager",
            name="智能库存管理助手",
            description="监控库存水平，预测补货需求，优化分配策略"
        )
        self.material_dao = MaterialDAO()
        self.allocation_dao = AllocationDAO()
        self.request_dao = RequestDAO()
        self.user_dao = UserDAO()
        self.department_dao = DepartmentDAO()
        
        # 库存阈值配置
        self.thresholds = {
            'critical': 0.1,    # 10%以下为紧急
            'low': 0.2,         # 20%以下为偏低
            'normal': 0.5,      # 50%以下为正常
            'high': 0.8         # 80%以上为充足
        }
    
    def process(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """处理库存管理请求"""
        try:
            action = input_data.get('action')
            
            if action == 'monitor_inventory':
                return self._monitor_inventory(input_data)
            elif action == 'predict_demand':
                return self._predict_demand(input_data)
            elif action == 'suggest_reorder':
                return self._suggest_reorder(input_data)
            elif action == 'optimize_allocation':
                return self._optimize_allocation(input_data)
            elif action == 'analyze_usage_pattern':
                return self._analyze_usage_pattern(input_data)
            elif action == 'inventory_alert':
                return self._inventory_alert(input_data)
            else:
                raise AgentError(f"不支持的操作: {action}", self.agent_id)
                
        except Exception as e:
            self.logger.error(f"处理请求失败: {e}")
            return AgentResponse(False, message=str(e)).to_dict()
    
    def _monitor_inventory(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """监控库存水平"""
        try:
            category = input_data.get('category')
            
            # 获取所有物资或指定类别物资
            if category:
                materials = self.material_dao.get_materials_by_category(category)
            else:
                materials = self.material_dao.get_all_materials()
            
            inventory_status = {
                'critical': [],
                'low': [],
                'normal': [],
                'high': [],
                'summary': {
                    'total_materials': len(materials),
                    'critical_count': 0,
                    'low_count': 0,
                    'normal_count': 0,
                    'high_count': 0
                }
            }
            
            for material in materials:
                # 计算库存比例
                if material.quantity > 0:
                    stock_ratio = material.remaining_quantity / material.quantity
                else:
                    stock_ratio = 0
                
                material_info = {
                    'id': material.id,
                    'name': material.name,
                    'category': material.category,
                    'total_quantity': material.quantity,
                    'remaining_quantity': material.remaining_quantity,
                    'stock_ratio': stock_ratio,
                    'status': self._get_stock_status(stock_ratio)
                }
                
                # 分类统计
                if stock_ratio <= self.thresholds['critical']:
                    inventory_status['critical'].append(material_info)
                    inventory_status['summary']['critical_count'] += 1
                elif stock_ratio <= self.thresholds['low']:
                    inventory_status['low'].append(material_info)
                    inventory_status['summary']['low_count'] += 1
                elif stock_ratio <= self.thresholds['normal']:
                    inventory_status['normal'].append(material_info)
                    inventory_status['summary']['normal_count'] += 1
                else:
                    inventory_status['high'].append(material_info)
                    inventory_status['summary']['high_count'] += 1
            
            # 生成警告信息
            warnings = []
            if inventory_status['summary']['critical_count'] > 0:
                warnings.append(f"{inventory_status['summary']['critical_count']} 个物资库存紧急")
            if inventory_status['summary']['low_count'] > 0:
                warnings.append(f"{inventory_status['summary']['low_count']} 个物资库存偏低")
            
            return AgentResponse(
                True,
                data=inventory_status,
                message="库存监控完成",
                suggestions=warnings,
                confidence=0.95
            ).to_dict()
            
        except Exception as e:
            return AgentResponse(False, message=f"库存监控失败: {e}").to_dict()
    
    def _predict_demand(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """预测需求"""
        material_id = input_data.get('material_id')
        days_ahead = input_data.get('days_ahead', 30)
        
        try:
            # 获取历史使用数据
            end_date = datetime.now()
            start_date = end_date - timedelta(days=90)  # 使用90天历史数据
            
            historical_usage = self.allocation_dao.get_material_usage_history(
                material_id, start_date, end_date
            )
            
            if not historical_usage:
                return AgentResponse(
                    False, 
                    message="缺少历史数据，无法进行需求预测"
                ).to_dict()
            
            # 计算日均使用量
            total_days = (end_date - start_date).days
            total_usage = sum(usage.get('quantity', 0) for usage in historical_usage)
            daily_avg = total_usage / total_days if total_days > 0 else 0
            
            # 考虑趋势因素
            trend_factor = self._calculate_trend_factor(historical_usage)
            
            # 考虑季节性因素
            seasonal_factor = self._calculate_seasonal_factor(material_id, end_date.month)
            
            # 预测未来需求
            predicted_daily_demand = daily_avg * trend_factor * seasonal_factor
            predicted_total_demand = predicted_daily_demand * days_ahead
            
            # 计算置信区间
            confidence_interval = self._calculate_confidence_interval(
                historical_usage, predicted_total_demand
            )
            
            prediction_result = {
                'material_id': material_id,
                'prediction_period_days': days_ahead,
                'historical_daily_avg': daily_avg,
                'trend_factor': trend_factor,
                'seasonal_factor': seasonal_factor,
                'predicted_daily_demand': predicted_daily_demand,
                'predicted_total_demand': predicted_total_demand,
                'confidence_interval': confidence_interval,
                'recommendation': self._generate_demand_recommendation(
                    predicted_total_demand, material_id
                )
            }
            
            return AgentResponse(
                True,
                data=prediction_result,
                message=f"预测未来{days_ahead}天需求量为 {predicted_total_demand:.1f}",
                confidence=0.8
            ).to_dict()
            
        except Exception as e:
            return AgentResponse(False, message=f"需求预测失败: {e}").to_dict()
    
    def _suggest_reorder(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """建议补货"""
        try:
            # 获取所有需要补货的物资
            materials = self.material_dao.get_all_materials()
            reorder_suggestions = []
            
            for material in materials:
                # 计算库存比例
                if material.quantity > 0:
                    stock_ratio = material.remaining_quantity / material.quantity
                else:
                    stock_ratio = 0
                
                # 只对库存偏低的物资进行补货建议
                if stock_ratio <= self.thresholds['low']:
                    # 预测未来30天需求
                    demand_prediction = self._predict_demand({
                        'material_id': material.id,
                        'days_ahead': 30
                    })
                    
                    if demand_prediction['success']:
                        predicted_demand = demand_prediction['data']['predicted_total_demand']
                        
                        # 计算建议补货量
                        safety_stock = predicted_demand * 0.2  # 20%安全库存
                        reorder_quantity = max(
                            predicted_demand + safety_stock - material.remaining_quantity,
                            material.quantity * 0.5  # 至少补充到50%
                        )
                        
                        if reorder_quantity > 0:
                            reorder_suggestions.append({
                                'material': {
                                    'id': material.id,
                                    'name': material.name,
                                    'category': material.category,
                                    'current_stock': material.remaining_quantity,
                                    'stock_ratio': stock_ratio
                                },
                                'predicted_demand': predicted_demand,
                                'suggested_reorder_quantity': math.ceil(reorder_quantity),
                                'priority': self._calculate_reorder_priority(stock_ratio, predicted_demand),
                                'estimated_cost': reorder_quantity * material.price if material.price else 0
                            })
            
            # 按优先级排序
            reorder_suggestions.sort(key=lambda x: x['priority'], reverse=True)
            
            total_cost = sum(s['estimated_cost'] for s in reorder_suggestions)
            
            return AgentResponse(
                True,
                data={
                    'reorder_suggestions': reorder_suggestions,
                    'total_items': len(reorder_suggestions),
                    'estimated_total_cost': total_cost
                },
                message=f"建议补货 {len(reorder_suggestions)} 种物资",
                confidence=0.85
            ).to_dict()
            
        except Exception as e:
            return AgentResponse(False, message=f"补货建议失败: {e}").to_dict()
    
    def _optimize_allocation(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """优化分配策略"""
        material_id = input_data.get('material_id')
        available_quantity = input_data.get('available_quantity')
        
        try:
            # 获取待审核的申请
            pending_requests = self.request_dao.get_pending_requests_for_material(material_id)
            
            if not pending_requests:
                return AgentResponse(
                    True,
                    data={'message': '没有待处理的申请'},
                    message="没有需要分配的申请"
                ).to_dict()
            
            # 计算各申请的优先级
            prioritized_requests = []
            for request in pending_requests:
                priority_score = self._calculate_allocation_priority(request)
                prioritized_requests.append({
                    'request': request,
                    'priority_score': priority_score
                })
            
            # 按优先级排序
            prioritized_requests.sort(key=lambda x: x['priority_score'], reverse=True)
            
            # 分配策略
            allocation_plan = []
            remaining_quantity = available_quantity
            
            for item in prioritized_requests:
                request = item['request']
                requested_quantity = request['quantity']
                
                if remaining_quantity >= requested_quantity:
                    # 完全满足
                    allocation_plan.append({
                        'request_id': request['id'],
                        'user_name': request.get('user_name', ''),
                        'department_name': request.get('department_name', ''),
                        'requested_quantity': requested_quantity,
                        'allocated_quantity': requested_quantity,
                        'allocation_ratio': 1.0,
                        'priority_score': item['priority_score'],
                        'status': 'fully_allocated'
                    })
                    remaining_quantity -= requested_quantity
                elif remaining_quantity > 0:
                    # 部分满足
                    allocation_plan.append({
                        'request_id': request['id'],
                        'user_name': request.get('user_name', ''),
                        'department_name': request.get('department_name', ''),
                        'requested_quantity': requested_quantity,
                        'allocated_quantity': remaining_quantity,
                        'allocation_ratio': remaining_quantity / requested_quantity,
                        'priority_score': item['priority_score'],
                        'status': 'partially_allocated'
                    })
                    remaining_quantity = 0
                else:
                    # 无法满足
                    allocation_plan.append({
                        'request_id': request['id'],
                        'user_name': request.get('user_name', ''),
                        'department_name': request.get('department_name', ''),
                        'requested_quantity': requested_quantity,
                        'allocated_quantity': 0,
                        'allocation_ratio': 0.0,
                        'priority_score': item['priority_score'],
                        'status': 'not_allocated'
                    })
            
            return AgentResponse(
                True,
                data={
                    'allocation_plan': allocation_plan,
                    'total_requests': len(allocation_plan),
                    'fully_satisfied': len([p for p in allocation_plan if p['status'] == 'fully_allocated']),
                    'partially_satisfied': len([p for p in allocation_plan if p['status'] == 'partially_allocated']),
                    'not_satisfied': len([p for p in allocation_plan if p['status'] == 'not_allocated']),
                    'remaining_quantity': remaining_quantity
                },
                message="分配策略优化完成",
                confidence=0.9
            ).to_dict()
            
        except Exception as e:
            return AgentResponse(False, message=f"分配优化失败: {e}").to_dict()
    
    def _analyze_usage_pattern(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """分析使用模式"""
        department_id = input_data.get('department_id')
        days = input_data.get('days', 90)
        
        try:
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days)
            
            # 获取部门使用数据
            if department_id:
                usage_data = self.allocation_dao.get_department_usage_pattern(
                    department_id, start_date, end_date
                )
            else:
                usage_data = self.allocation_dao.get_overall_usage_pattern(
                    start_date, end_date
                )
            
            # 分析模式
            pattern_analysis = {
                'peak_usage_day': self._find_peak_usage_day(usage_data),
                'peak_usage_hour': self._find_peak_usage_hour(usage_data),
                'most_requested_materials': self._find_most_requested_materials(usage_data),
                'usage_trend': self._analyze_usage_trend(usage_data),
                'seasonal_pattern': self._analyze_seasonal_pattern(usage_data)
            }
            
            return AgentResponse(
                True,
                data=pattern_analysis,
                message="使用模式分析完成",
                confidence=0.8
            ).to_dict()
            
        except Exception as e:
            return AgentResponse(False, message=f"使用模式分析失败: {e}").to_dict()
    
    def _inventory_alert(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """库存警报"""
        try:
            # 获取库存状态
            inventory_status = self._monitor_inventory({})
            
            if not inventory_status['success']:
                return inventory_status
            
            alerts = []
            data = inventory_status['data']
            
            # 生成警报
            for material in data['critical']:
                alerts.append({
                    'level': 'critical',
                    'material_id': material['id'],
                    'material_name': material['name'],
                    'message': f"{material['name']} 库存紧急 ({material['remaining_quantity']}/{material['total_quantity']})",
                    'action_required': '立即补货'
                })
            
            for material in data['low']:
                alerts.append({
                    'level': 'warning',
                    'material_id': material['id'],
                    'material_name': material['name'],
                    'message': f"{material['name']} 库存偏低 ({material['remaining_quantity']}/{material['total_quantity']})",
                    'action_required': '计划补货'
                })
            
            return AgentResponse(
                True,
                data={'alerts': alerts, 'alert_count': len(alerts)},
                message=f"生成 {len(alerts)} 个库存警报",
                confidence=0.95
            ).to_dict()
            
        except Exception as e:
            return AgentResponse(False, message=f"库存警报失败: {e}").to_dict()
    
    def get_capabilities(self) -> List[str]:
        """获取智能体能力列表"""
        return [
            "库存监控",
            "需求预测",
            "补货建议",
            "分配优化",
            "使用模式分析",
            "库存警报"
        ]
    
    # 辅助方法
    def _get_stock_status(self, stock_ratio):
        """获取库存状态"""
        if stock_ratio <= self.thresholds['critical']:
            return 'critical'
        elif stock_ratio <= self.thresholds['low']:
            return 'low'
        elif stock_ratio <= self.thresholds['normal']:
            return 'normal'
        else:
            return 'high'
    
    def _calculate_trend_factor(self, historical_usage):
        """计算趋势因子"""
        if len(historical_usage) < 2:
            return 1.0
        
        # 简单的线性趋势计算
        recent_usage = sum(u.get('quantity', 0) for u in historical_usage[-30:])
        early_usage = sum(u.get('quantity', 0) for u in historical_usage[:30])
        
        if early_usage == 0:
            return 1.0
        
        trend = recent_usage / early_usage
        return max(0.5, min(2.0, trend))  # 限制在0.5-2.0之间
    
    def _calculate_seasonal_factor(self, material_id, month):
        """计算季节性因子"""
        # 简化的季节性调整
        seasonal_factors = {
            1: 1.1, 2: 1.0, 3: 1.0, 4: 0.9, 5: 0.9, 6: 0.8,
            7: 0.8, 8: 0.8, 9: 1.0, 10: 1.1, 11: 1.2, 12: 1.3
        }
        return seasonal_factors.get(month, 1.0)
    
    def _calculate_confidence_interval(self, historical_usage, predicted_demand):
        """计算置信区间"""
        if not historical_usage:
            return {'lower': predicted_demand * 0.8, 'upper': predicted_demand * 1.2}
        
        quantities = [u.get('quantity', 0) for u in historical_usage]
        std_dev = (sum((x - sum(quantities)/len(quantities))**2 for x in quantities) / len(quantities))**0.5
        
        return {
            'lower': max(0, predicted_demand - 1.96 * std_dev),
            'upper': predicted_demand + 1.96 * std_dev
        }
    
    def _generate_demand_recommendation(self, predicted_demand, material_id):
        """生成需求建议"""
        material = self.material_dao.get_material_by_id(material_id)
        if not material:
            return "无法获取物资信息"
        
        if predicted_demand > material.remaining_quantity:
            return f"建议补货 {predicted_demand - material.remaining_quantity:.0f} 个单位"
        else:
            return "当前库存充足"
    
    def _calculate_reorder_priority(self, stock_ratio, predicted_demand):
        """计算补货优先级"""
        # 库存比例越低，预测需求越高，优先级越高
        stock_priority = (1 - stock_ratio) * 0.7
        demand_priority = min(predicted_demand / 100, 1.0) * 0.3
        return stock_priority + demand_priority
    
    def _calculate_allocation_priority(self, request):
        """计算分配优先级"""
        # 简化的优先级计算
        base_priority = 0.5
        
        # 申请时间越早，优先级越高
        days_since_request = (datetime.now() - request.get('request_date', datetime.now())).days
        time_priority = min(days_since_request / 30, 0.3)
        
        # 申请数量适中的优先级较高
        quantity_priority = 0.2 if request.get('quantity', 0) <= 10 else 0.1
        
        return base_priority + time_priority + quantity_priority
    
    def _find_peak_usage_day(self, usage_data):
        """找出使用高峰日"""
        # 简化实现
        return "周三"
    
    def _find_peak_usage_hour(self, usage_data):
        """找出使用高峰时段"""
        # 简化实现
        return "14:00-16:00"
    
    def _find_most_requested_materials(self, usage_data):
        """找出最常申请的物资"""
        # 简化实现
        return ["A4打印纸", "签字笔", "订书钉"]
    
    def _analyze_usage_trend(self, usage_data):
        """分析使用趋势"""
        # 简化实现
        return "稳定"
    
    def _analyze_seasonal_pattern(self, usage_data):
        """分析季节性模式"""
        # 简化实现
        return "年末使用量增加"
