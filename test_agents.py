#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能体系统测试脚本
"""

from agents.agent_coordinator import AgentCoordinator
from agents.request_assistant_agent import RequestAssistantAgent
from agents.approval_assistant_agent import ApprovalAssistantAgent
from agents.inventory_manager_agent import InventoryManagerAgent
from agents.report_analyst_agent import ReportAnalystAgent
import json

def test_agent_coordinator():
    """测试智能体协调中心"""
    print("=== 测试智能体协调中心 ===")
    
    coordinator = AgentCoordinator()
    
    # 测试获取智能体状态
    print("\n1. 测试获取智能体状态")
    result = coordinator.process({'action': 'agent_status'})
    print(f"结果: {json.dumps(result, indent=2, ensure_ascii=False)}")
    
    # 测试系统健康检查
    print("\n2. 测试系统健康检查")
    result = coordinator.process({'action': 'monitor_system_health'})
    print(f"结果: {json.dumps(result, indent=2, ensure_ascii=False)}")
    
    # 测试生成综合报告
    print("\n3. 测试生成综合报告")
    result = coordinator.process({
        'action': 'generate_comprehensive_report',
        'period_days': 30,
        'report_type': 'full'
    })
    print(f"结果: {json.dumps(result, indent=2, ensure_ascii=False)}")

def test_request_assistant():
    """测试智能申请助手"""
    print("\n=== 测试智能申请助手 ===")
    
    agent = RequestAssistantAgent()
    
    # 测试物资建议
    print("\n1. 测试物资建议")
    result = agent.process({
        'action': 'suggest_materials',
        'user_id': 1,
        'department_id': 1,
        'category': '办公用品'
    })
    print(f"结果: {json.dumps(result, indent=2, ensure_ascii=False)}")
    
    # 测试数量预测
    print("\n2. 测试数量预测")
    result = agent.process({
        'action': 'predict_quantity',
        'user_id': 1,
        'material_id': 1
    })
    print(f"结果: {json.dumps(result, indent=2, ensure_ascii=False)}")

def test_approval_assistant():
    """测试智能审批助手"""
    print("\n=== 测试智能审批助手 ===")
    
    agent = ApprovalAssistantAgent()
    
    # 测试申请分析
    print("\n1. 测试申请分析")
    result = agent.process({
        'action': 'analyze_request',
        'request_id': 1
    })
    print(f"结果: {json.dumps(result, indent=2, ensure_ascii=False)}")

def test_inventory_manager():
    """测试智能库存管理"""
    print("\n=== 测试智能库存管理 ===")
    
    agent = InventoryManagerAgent()
    
    # 测试库存监控
    print("\n1. 测试库存监控")
    result = agent.process({
        'action': 'monitor_inventory'
    })
    print(f"结果: {json.dumps(result, indent=2, ensure_ascii=False)}")
    
    # 测试需求预测
    print("\n2. 测试需求预测")
    result = agent.process({
        'action': 'predict_demand',
        'days_ahead': 30
    })
    print(f"结果: {json.dumps(result, indent=2, ensure_ascii=False)}")

def test_report_analyst():
    """测试智能报表分析"""
    print("\n=== 测试智能报表分析 ===")
    
    agent = ReportAnalystAgent()
    
    # 测试生成洞察
    print("\n1. 测试生成洞察")
    result = agent.process({
        'action': 'generate_insights',
        'period_days': 30
    })
    print(f"结果: {json.dumps(result, indent=2, ensure_ascii=False)}")
    
    # 测试趋势分析
    print("\n2. 测试趋势分析")
    result = agent.process({
        'action': 'trend_analysis',
        'metric': 'requests',
        'period_days': 90
    })
    print(f"结果: {json.dumps(result, indent=2, ensure_ascii=False)}")

def test_workflow_coordination():
    """测试工作流程协调"""
    print("\n=== 测试工作流程协调 ===")
    
    coordinator = AgentCoordinator()
    
    # 测试申请处理工作流程
    print("\n1. 测试申请处理工作流程")
    result = coordinator.process({
        'action': 'coordinate_request_process',
        'request_data': {
            'request_id': 1,
            'material_id': 1,
            'user_id': 1,
            'department_id': 1,
            'quantity': 5,
            'reason': '办公需要'
        }
    })
    print(f"结果: {json.dumps(result, indent=2, ensure_ascii=False)}")

if __name__ == '__main__':
    try:
        print("开始测试智能体系统...")
        
        # 测试各个智能体
        test_request_assistant()
        test_approval_assistant()
        test_inventory_manager()
        test_report_analyst()
        
        # 测试协调中心
        test_agent_coordinator()
        
        # 测试工作流程协调
        test_workflow_coordination()
        
        print("\n=== 所有测试完成 ===")
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
