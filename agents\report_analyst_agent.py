#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能报表分析Agent
自动生成洞察报告、趋势分析、异常检测
"""

import json
import statistics
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from agents.base_agent import BaseAgent, AgentResponse, AgentError
from dao.material_dao import MaterialDAO
from dao.allocation_dao import AllocationDAO, RequestDAO
from dao.user_dao import UserDAO
from dao.department_dao import DepartmentDAO

class ReportAnalystAgent(BaseAgent):
    """智能报表分析助手"""
    
    def __init__(self):
        super().__init__(
            agent_id="report_analyst",
            name="智能报表分析助手",
            description="自动生成洞察报告、趋势分析、异常检测"
        )
        self.material_dao = MaterialDAO()
        self.allocation_dao = AllocationDAO()
        self.request_dao = RequestDAO()
        self.user_dao = UserDAO()
        self.department_dao = DepartmentDAO()
    
    def process(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """处理报表分析请求"""
        try:
            action = input_data.get('action')
            
            if action == 'generate_insights':
                return self._generate_insights(input_data)
            elif action == 'trend_analysis':
                return self._trend_analysis(input_data)
            elif action == 'anomaly_detection':
                return self._anomaly_detection(input_data)
            elif action == 'performance_analysis':
                return self._performance_analysis(input_data)
            elif action == 'cost_analysis':
                return self._cost_analysis(input_data)
            elif action == 'efficiency_analysis':
                return self._efficiency_analysis(input_data)
            else:
                raise AgentError(f"不支持的操作: {action}", self.agent_id)
                
        except Exception as e:
            self.logger.error(f"处理请求失败: {e}")
            return AgentResponse(False, message=str(e)).to_dict()
    
    def _generate_insights(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """生成洞察报告"""
        period_days = input_data.get('period_days', 30)
        
        try:
            end_date = datetime.now()
            start_date = end_date - timedelta(days=period_days)
            
            # 收集各种数据
            material_stats = self._get_material_statistics(start_date, end_date)
            request_stats = self._get_request_statistics(start_date, end_date)
            allocation_stats = self._get_allocation_statistics(start_date, end_date)
            department_stats = self._get_department_statistics(start_date, end_date)
            
            # 生成洞察
            insights = {
                'period': {
                    'start_date': start_date.isoformat(),
                    'end_date': end_date.isoformat(),
                    'days': period_days
                },
                'key_metrics': {
                    'total_requests': request_stats['total_requests'],
                    'approved_requests': request_stats['approved_requests'],
                    'approval_rate': request_stats['approval_rate'],
                    'total_allocations': allocation_stats['total_allocations'],
                    'total_value_allocated': allocation_stats['total_value']
                },
                'top_insights': self._extract_key_insights(
                    material_stats, request_stats, allocation_stats, department_stats
                ),
                'recommendations': self._generate_recommendations(
                    material_stats, request_stats, allocation_stats, department_stats
                ),
                'alerts': self._generate_alerts(
                    material_stats, request_stats, allocation_stats
                )
            }
            
            return AgentResponse(
                True,
                data=insights,
                message=f"生成了 {period_days} 天的洞察报告",
                confidence=0.9
            ).to_dict()
            
        except Exception as e:
            return AgentResponse(False, message=f"生成洞察报告失败: {e}").to_dict()
    
    def _trend_analysis(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """趋势分析"""
        metric = input_data.get('metric', 'requests')  # requests, allocations, costs
        period_days = input_data.get('period_days', 90)
        
        try:
            end_date = datetime.now()
            start_date = end_date - timedelta(days=period_days)
            
            # 按周分组数据
            weekly_data = self._get_weekly_data(metric, start_date, end_date)
            
            # 计算趋势
            trend_analysis = {
                'metric': metric,
                'period_days': period_days,
                'weekly_data': weekly_data,
                'trend_direction': self._calculate_trend_direction(weekly_data),
                'trend_strength': self._calculate_trend_strength(weekly_data),
                'growth_rate': self._calculate_growth_rate(weekly_data),
                'forecast': self._forecast_next_period(weekly_data),
                'seasonality': self._detect_seasonality(weekly_data)
            }
            
            return AgentResponse(
                True,
                data=trend_analysis,
                message=f"{metric} 趋势分析完成",
                confidence=0.8
            ).to_dict()
            
        except Exception as e:
            return AgentResponse(False, message=f"趋势分析失败: {e}").to_dict()
    
    def _anomaly_detection(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """异常检测"""
        try:
            # 检测各种异常
            anomalies = {
                'request_anomalies': self._detect_request_anomalies(),
                'allocation_anomalies': self._detect_allocation_anomalies(),
                'inventory_anomalies': self._detect_inventory_anomalies(),
                'user_behavior_anomalies': self._detect_user_behavior_anomalies(),
                'department_anomalies': self._detect_department_anomalies()
            }
            
            # 统计异常数量
            total_anomalies = sum(len(anomaly_list) for anomaly_list in anomalies.values())
            
            # 按严重程度分类
            severity_counts = {'high': 0, 'medium': 0, 'low': 0}
            for anomaly_list in anomalies.values():
                for anomaly in anomaly_list:
                    severity_counts[anomaly.get('severity', 'low')] += 1
            
            return AgentResponse(
                True,
                data={
                    'anomalies': anomalies,
                    'summary': {
                        'total_anomalies': total_anomalies,
                        'severity_distribution': severity_counts
                    }
                },
                message=f"检测到 {total_anomalies} 个异常",
                confidence=0.85
            ).to_dict()
            
        except Exception as e:
            return AgentResponse(False, message=f"异常检测失败: {e}").to_dict()
    
    def _performance_analysis(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """性能分析"""
        try:
            # 分析各种性能指标
            performance_metrics = {
                'request_processing': self._analyze_request_processing_performance(),
                'approval_efficiency': self._analyze_approval_efficiency(),
                'allocation_speed': self._analyze_allocation_speed(),
                'inventory_turnover': self._analyze_inventory_turnover(),
                'user_satisfaction': self._analyze_user_satisfaction()
            }
            
            # 计算综合性能分数
            overall_score = self._calculate_overall_performance_score(performance_metrics)
            
            return AgentResponse(
                True,
                data={
                    'performance_metrics': performance_metrics,
                    'overall_score': overall_score,
                    'performance_grade': self._get_performance_grade(overall_score)
                },
                message="性能分析完成",
                confidence=0.8
            ).to_dict()
            
        except Exception as e:
            return AgentResponse(False, message=f"性能分析失败: {e}").to_dict()
    
    def _cost_analysis(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """成本分析"""
        period_days = input_data.get('period_days', 30)
        
        try:
            end_date = datetime.now()
            start_date = end_date - timedelta(days=period_days)
            
            # 成本分析
            cost_analysis = {
                'total_allocation_cost': self._calculate_total_allocation_cost(start_date, end_date),
                'cost_by_category': self._calculate_cost_by_category(start_date, end_date),
                'cost_by_department': self._calculate_cost_by_department(start_date, end_date),
                'cost_trends': self._analyze_cost_trends(start_date, end_date),
                'cost_efficiency': self._analyze_cost_efficiency(start_date, end_date)
            }
            
            return AgentResponse(
                True,
                data=cost_analysis,
                message="成本分析完成",
                confidence=0.9
            ).to_dict()
            
        except Exception as e:
            return AgentResponse(False, message=f"成本分析失败: {e}").to_dict()
    
    def _efficiency_analysis(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """效率分析"""
        try:
            efficiency_metrics = {
                'request_to_approval_time': self._calculate_avg_approval_time(),
                'approval_to_allocation_time': self._calculate_avg_allocation_time(),
                'inventory_utilization': self._calculate_inventory_utilization(),
                'department_efficiency': self._analyze_department_efficiency(),
                'resource_optimization': self._analyze_resource_optimization()
            }
            
            return AgentResponse(
                True,
                data=efficiency_metrics,
                message="效率分析完成",
                confidence=0.85
            ).to_dict()
            
        except Exception as e:
            return AgentResponse(False, message=f"效率分析失败: {e}").to_dict()
    
    def get_capabilities(self) -> List[str]:
        """获取智能体能力列表"""
        return [
            "洞察报告生成",
            "趋势分析",
            "异常检测",
            "性能分析",
            "成本分析",
            "效率分析"
        ]
    
    # 辅助方法
    def _get_material_statistics(self, start_date, end_date):
        """获取物资统计"""
        # 简化实现
        return {
            'total_materials': 66,
            'active_materials': 60,
            'low_stock_materials': 5
        }
    
    def _get_request_statistics(self, start_date, end_date):
        """获取申请统计"""
        # 简化实现
        return {
            'total_requests': 25,
            'approved_requests': 20,
            'rejected_requests': 3,
            'pending_requests': 2,
            'approval_rate': 0.8
        }
    
    def _get_allocation_statistics(self, start_date, end_date):
        """获取分配统计"""
        # 简化实现
        return {
            'total_allocations': 20,
            'total_quantity': 150,
            'total_value': 25000
        }
    
    def _get_department_statistics(self, start_date, end_date):
        """获取部门统计"""
        # 简化实现
        return {
            'most_active_department': '技术部',
            'highest_value_department': '财务部'
        }
    
    def _extract_key_insights(self, material_stats, request_stats, allocation_stats, department_stats):
        """提取关键洞察"""
        insights = []
        
        if request_stats['approval_rate'] > 0.9:
            insights.append("申请审批通过率很高，流程运行良好")
        elif request_stats['approval_rate'] < 0.6:
            insights.append("申请审批通过率偏低，需要关注申请质量")
        
        if material_stats['low_stock_materials'] > 10:
            insights.append("多个物资库存偏低，需要及时补货")
        
        if allocation_stats['total_value'] > 50000:
            insights.append("本期分配价值较高，注意成本控制")
        
        return insights
    
    def _generate_recommendations(self, material_stats, request_stats, allocation_stats, department_stats):
        """生成建议"""
        recommendations = []
        
        if request_stats['approval_rate'] < 0.7:
            recommendations.append("建议加强申请前的指导，提高申请质量")
        
        if material_stats['low_stock_materials'] > 5:
            recommendations.append("建议制定补货计划，避免缺货影响工作")
        
        recommendations.append("建议定期分析使用模式，优化库存配置")
        
        return recommendations
    
    def _generate_alerts(self, material_stats, request_stats, allocation_stats):
        """生成警报"""
        alerts = []
        
        if material_stats['low_stock_materials'] > 10:
            alerts.append({
                'level': 'warning',
                'message': '多个物资库存不足',
                'action': '立即检查库存并安排补货'
            })
        
        if request_stats['approval_rate'] < 0.5:
            alerts.append({
                'level': 'critical',
                'message': '申请通过率过低',
                'action': '检查审批流程和申请质量'
            })
        
        return alerts
    
    def _get_weekly_data(self, metric, start_date, end_date):
        """获取周度数据"""
        # 简化实现
        weeks = []
        current_date = start_date
        week_num = 1
        
        while current_date < end_date:
            week_end = min(current_date + timedelta(days=7), end_date)
            weeks.append({
                'week': week_num,
                'start_date': current_date.isoformat(),
                'end_date': week_end.isoformat(),
                'value': week_num * 10 + (week_num % 3) * 5  # 模拟数据
            })
            current_date = week_end
            week_num += 1
        
        return weeks
    
    def _calculate_trend_direction(self, weekly_data):
        """计算趋势方向"""
        if len(weekly_data) < 2:
            return 'stable'
        
        values = [week['value'] for week in weekly_data]
        first_half = values[:len(values)//2]
        second_half = values[len(values)//2:]
        
        first_avg = sum(first_half) / len(first_half)
        second_avg = sum(second_half) / len(second_half)
        
        if second_avg > first_avg * 1.1:
            return 'increasing'
        elif second_avg < first_avg * 0.9:
            return 'decreasing'
        else:
            return 'stable'
    
    def _calculate_trend_strength(self, weekly_data):
        """计算趋势强度"""
        if len(weekly_data) < 2:
            return 0
        
        values = [week['value'] for week in weekly_data]
        # 简化的趋势强度计算
        return min(1.0, abs(values[-1] - values[0]) / max(values[0], 1) * 0.5)
    
    def _calculate_growth_rate(self, weekly_data):
        """计算增长率"""
        if len(weekly_data) < 2:
            return 0
        
        first_value = weekly_data[0]['value']
        last_value = weekly_data[-1]['value']
        
        if first_value == 0:
            return 0
        
        return (last_value - first_value) / first_value
    
    def _forecast_next_period(self, weekly_data):
        """预测下一期"""
        if len(weekly_data) < 2:
            return weekly_data[-1]['value'] if weekly_data else 0
        
        # 简单的线性预测
        values = [week['value'] for week in weekly_data]
        trend = (values[-1] - values[0]) / len(values)
        return max(0, values[-1] + trend)
    
    def _detect_seasonality(self, weekly_data):
        """检测季节性"""
        # 简化实现
        return {
            'has_seasonality': False,
            'pattern': 'none'
        }
    
    def _detect_request_anomalies(self):
        """检测申请异常"""
        # 简化实现
        return [
            {
                'type': 'unusual_quantity',
                'description': '用户张三申请数量异常',
                'severity': 'medium',
                'details': '申请数量超过平均值3倍'
            }
        ]
    
    def _detect_allocation_anomalies(self):
        """检测分配异常"""
        return []
    
    def _detect_inventory_anomalies(self):
        """检测库存异常"""
        return [
            {
                'type': 'low_stock',
                'description': '多个物资库存偏低',
                'severity': 'high',
                'details': '5个物资库存低于安全线'
            }
        ]
    
    def _detect_user_behavior_anomalies(self):
        """检测用户行为异常"""
        return []
    
    def _detect_department_anomalies(self):
        """检测部门异常"""
        return []
    
    def _analyze_request_processing_performance(self):
        """分析申请处理性能"""
        return {
            'avg_processing_time': 2.5,  # 天
            'score': 0.8
        }
    
    def _analyze_approval_efficiency(self):
        """分析审批效率"""
        return {
            'avg_approval_time': 1.2,  # 天
            'score': 0.9
        }
    
    def _analyze_allocation_speed(self):
        """分析分配速度"""
        return {
            'avg_allocation_time': 0.5,  # 天
            'score': 0.95
        }
    
    def _analyze_inventory_turnover(self):
        """分析库存周转"""
        return {
            'turnover_rate': 0.3,  # 月
            'score': 0.7
        }
    
    def _analyze_user_satisfaction(self):
        """分析用户满意度"""
        return {
            'satisfaction_score': 0.85,
            'score': 0.85
        }
    
    def _calculate_overall_performance_score(self, performance_metrics):
        """计算综合性能分数"""
        scores = [metric['score'] for metric in performance_metrics.values()]
        return sum(scores) / len(scores)
    
    def _get_performance_grade(self, score):
        """获取性能等级"""
        if score >= 0.9:
            return 'A'
        elif score >= 0.8:
            return 'B'
        elif score >= 0.7:
            return 'C'
        elif score >= 0.6:
            return 'D'
        else:
            return 'F'
    
    def _calculate_total_allocation_cost(self, start_date, end_date):
        """计算总分配成本"""
        return 25000  # 简化实现
    
    def _calculate_cost_by_category(self, start_date, end_date):
        """按类别计算成本"""
        return {
            'fixed_asset': 15000,
            'consumable': 10000
        }
    
    def _calculate_cost_by_department(self, start_date, end_date):
        """按部门计算成本"""
        return {
            '技术部': 8000,
            '财务部': 7000,
            '人事部': 5000,
            '市场部': 3000,
            '行政部': 2000
        }
    
    def _analyze_cost_trends(self, start_date, end_date):
        """分析成本趋势"""
        return {
            'trend': 'increasing',
            'growth_rate': 0.15
        }
    
    def _analyze_cost_efficiency(self, start_date, end_date):
        """分析成本效率"""
        return {
            'cost_per_request': 1000,
            'efficiency_score': 0.8
        }
    
    def _calculate_avg_approval_time(self):
        """计算平均审批时间"""
        return 1.2  # 天
    
    def _calculate_avg_allocation_time(self):
        """计算平均分配时间"""
        return 0.5  # 天
    
    def _calculate_inventory_utilization(self):
        """计算库存利用率"""
        return 0.75
    
    def _analyze_department_efficiency(self):
        """分析部门效率"""
        return {
            '技术部': 0.9,
            '财务部': 0.85,
            '人事部': 0.8,
            '市场部': 0.75,
            '行政部': 0.7
        }
    
    def _analyze_resource_optimization(self):
        """分析资源优化"""
        return {
            'optimization_score': 0.8,
            'suggestions': ['优化库存配置', '改进分配策略']
        }
