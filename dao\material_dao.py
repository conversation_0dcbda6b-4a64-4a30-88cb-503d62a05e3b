from dao.database import db
from models.material import Material
from datetime import datetime

class MaterialDAO:
    def __init__(self):
        self.db = db
    
    def create_material(self, material):
        """创建物资"""
        # 如果是固定资产，生成资产编号
        if material.is_fixed_asset():
            material.generate_asset_number()
        
        sql = """
        INSERT INTO materials (asset_number, name, model, category, price, purchase_date,
                             supplier, purchase_amount, quantity, remaining_quantity, 
                             status, description)
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """
        params = (material.asset_number, material.name, material.model, material.category,
                 material.price, material.purchase_date, material.supplier, 
                 material.purchase_amount, material.quantity, material.remaining_quantity,
                 material.status, material.description)
        material_id = self.db.execute_insert(sql, params)
        material.id = material_id
        return material
    
    def get_material_by_id(self, material_id):
        """根据ID获取物资"""
        sql = "SELECT * FROM materials WHERE id = %s"
        result = self.db.execute_query_one(sql, (material_id,))
        return Material.from_dict(result) if result else None
    
    def get_material_by_asset_number(self, asset_number):
        """根据资产编号获取物资"""
        sql = "SELECT * FROM materials WHERE asset_number = %s"
        result = self.db.execute_query_one(sql, (asset_number,))
        return Material.from_dict(result) if result else None
    
    def get_materials_by_category(self, category):
        """根据类别获取物资列表"""
        sql = "SELECT * FROM materials WHERE category = %s ORDER BY created_at DESC"
        results = self.db.execute_query(sql, (category,))
        return [Material.from_dict(row) for row in results]
    
    def get_all_materials(self):
        """获取所有物资"""
        sql = "SELECT * FROM materials ORDER BY created_at DESC"
        results = self.db.execute_query(sql)
        return [Material.from_dict(row) for row in results]
    
    def search_materials(self, keyword=None, category=None, min_price=None, max_price=None):
        """搜索物资"""
        sql = "SELECT * FROM materials WHERE 1=1"
        params = []
        
        if keyword:
            sql += " AND (name LIKE %s OR model LIKE %s OR supplier LIKE %s)"
            keyword_param = f"%{keyword}%"
            params.extend([keyword_param, keyword_param, keyword_param])
        
        if category:
            sql += " AND category = %s"
            params.append(category)
        
        if min_price is not None:
            sql += " AND price >= %s"
            params.append(min_price)
        
        if max_price is not None:
            sql += " AND price <= %s"
            params.append(max_price)
        
        sql += " ORDER BY created_at DESC"
        results = self.db.execute_query(sql, params)
        return [Material.from_dict(row) for row in results]
    
    def update_material(self, material):
        """更新物资信息"""
        sql = """
        UPDATE materials SET name = %s, model = %s, category = %s, price = %s,
               purchase_date = %s, supplier = %s, purchase_amount = %s, quantity = %s,
               remaining_quantity = %s, status = %s, description = %s, updated_at = %s
        WHERE id = %s
        """
        params = (material.name, material.model, material.category, material.price,
                 material.purchase_date, material.supplier, material.purchase_amount,
                 material.quantity, material.remaining_quantity, material.status,
                 material.description, datetime.now(), material.id)
        return self.db.execute_update(sql, params)
    
    def update_remaining_quantity(self, material_id, quantity_change):
        """更新剩余数量"""
        sql = """
        UPDATE materials SET remaining_quantity = remaining_quantity + %s, updated_at = %s
        WHERE id = %s
        """
        params = (quantity_change, datetime.now(), material_id)
        return self.db.execute_update(sql, params)
    
    def scrap_material(self, material_id, reason):
        """报废物资"""
        sql = """
        UPDATE materials SET status = 'scrapped', scrap_reason = %s, 
               scrap_date = %s, updated_at = %s
        WHERE id = %s
        """
        params = (reason, datetime.now().date(), datetime.now(), material_id)
        return self.db.execute_update(sql, params)
    
    def delete_material(self, material_id):
        """删除物资"""
        sql = "DELETE FROM materials WHERE id = %s"
        return self.db.execute_update(sql, (material_id,))
    
    def get_material_statistics(self):
        """获取物资统计信息"""
        sql = """
        SELECT 
            category,
            COUNT(*) as total_count,
            SUM(quantity) as total_quantity,
            SUM(remaining_quantity) as remaining_quantity,
            SUM(price * quantity) as total_value,
            AVG(price) as avg_price
        FROM materials
        GROUP BY category
        """
        return self.db.execute_query(sql)
