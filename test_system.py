#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统功能测试脚本
测试物资管理系统的主要功能
"""

import requests
import json
from datetime import datetime

class SystemTester:
    def __init__(self, base_url='http://localhost:5000'):
        self.base_url = base_url
        self.session = requests.Session()
        
    def test_login(self, username, password):
        """测试登录功能"""
        print(f"测试登录: {username}")
        
        # 获取登录页面
        response = self.session.get(f"{self.base_url}/auth/login")
        if response.status_code != 200:
            print(f"❌ 获取登录页面失败: {response.status_code}")
            return False
            
        # 提交登录表单
        login_data = {
            'username': username,
            'password': password
        }
        response = self.session.post(f"{self.base_url}/auth/login", data=login_data)
        
        if response.status_code == 200 and 'login' not in response.url:
            print(f"✅ 登录成功")
            return True
        else:
            print(f"❌ 登录失败")
            return False
    
    def test_dashboard(self):
        """测试仪表板"""
        print("测试仪表板访问")
        response = self.session.get(f"{self.base_url}/dashboard")
        
        if response.status_code == 200:
            print("✅ 仪表板访问成功")
            return True
        else:
            print(f"❌ 仪表板访问失败: {response.status_code}")
            return False
    
    def test_material_list(self):
        """测试物资列表"""
        print("测试物资列表")
        response = self.session.get(f"{self.base_url}/materials")
        
        if response.status_code == 200:
            print("✅ 物资列表访问成功")
            return True
        else:
            print(f"❌ 物资列表访问失败: {response.status_code}")
            return False
    
    def test_reports(self):
        """测试报表功能"""
        print("测试报表功能")
        response = self.session.get(f"{self.base_url}/reports")
        
        if response.status_code == 200:
            print("✅ 报表页面访问成功")
            return True
        else:
            print(f"❌ 报表页面访问失败: {response.status_code}")
            return False
    
    def test_logout(self):
        """测试登出功能"""
        print("测试登出功能")
        response = self.session.get(f"{self.base_url}/auth/logout")
        
        if response.status_code == 200 or 'login' in response.url:
            print("✅ 登出成功")
            return True
        else:
            print(f"❌ 登出失败: {response.status_code}")
            return False
    
    def run_tests(self):
        """运行所有测试"""
        print("=" * 50)
        print("开始系统功能测试")
        print("=" * 50)
        
        # 测试管理员账号
        print("\n--- 测试管理员功能 ---")
        if self.test_login('admin', 'admin123'):
            self.test_dashboard()
            self.test_material_list()
            self.test_reports()
            self.test_logout()
        
        # 测试普通员工账号
        print("\n--- 测试员工功能 ---")
        if self.test_login('zhangsan', 'password123'):
            self.test_dashboard()
            self.test_material_list()
            self.test_reports()
            self.test_logout()
        
        print("\n" + "=" * 50)
        print("测试完成")
        print("=" * 50)

def main():
    """主函数"""
    print("物资管理系统功能测试")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    tester = SystemTester()
    
    try:
        # 首先检查服务器是否运行
        response = requests.get('http://localhost:5000', timeout=5)
        if response.status_code == 200:
            print("✅ 服务器运行正常")
            tester.run_tests()
        else:
            print("❌ 服务器响应异常")
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到服务器，请确保Flask应用正在运行")
        print("请运行: python app.py")
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")

if __name__ == '__main__':
    main()
