{% extends "base.html" %}

{% block title %}物资报表{% endblock %}

{% block content %}
<div class="container">
    <div class="card">
        <div class="card-header">
            <h2 class="card-title">物资报表</h2>
            <p class="text-muted">查看物资统计信息和生成报表</p>
        </div>
        
        <!-- 统计概览 -->
        {% if statistics %}
        <div class="stats-overview">
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-boxes"></i>
                    </div>
                    <div class="stat-content">
                        <h3>{{ statistics.total_materials }}</h3>
                        <p>物资总数</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-desktop"></i>
                    </div>
                    <div class="stat-content">
                        <h3>{{ statistics.fixed_assets }}</h3>
                        <p>固定资产</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-shopping-cart"></i>
                    </div>
                    <div class="stat-content">
                        <h3>{{ statistics.consumables }}</h3>
                        <p>消耗品</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-dollar-sign"></i>
                    </div>
                    <div class="stat-content">
                        <h3>¥{{ "%.2f"|format(statistics.total_value) }}</h3>
                        <p>总价值</p>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}
        
        <!-- 分类统计 -->
        {% if category_stats %}
        <div class="category-section">
            <h3>分类统计</h3>
            <div class="category-grid">
                {% for category in category_stats %}
                <div class="category-card">
                    <h4>{{ category.category_name }}</h4>
                    <div class="category-stats">
                        <div class="stat-item">
                            <span class="label">数量:</span>
                            <span class="value">{{ category.count }}</span>
                        </div>
                        <div class="stat-item">
                            <span class="label">总价值:</span>
                            <span class="value">¥{{ "%.2f"|format(category.total_value) }}</span>
                        </div>
                        <div class="stat-item">
                            <span class="label">平均价格:</span>
                            <span class="value">¥{{ "%.2f"|format(category.avg_price) }}</span>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
        {% endif %}
        
        <!-- 导出功能 -->
        <div class="export-section">
            <h3>导出报表</h3>
            <div class="export-buttons">
                <a href="{{ url_for('report.export_material_inventory') }}" class="btn btn-success">
                    <i class="fas fa-download"></i> 导出物资台账
                </a>
                <a href="{{ url_for('report.export_material_inventory', category='fixed_asset') }}" class="btn btn-info">
                    <i class="fas fa-download"></i> 导出固定资产
                </a>
                <a href="{{ url_for('report.export_material_inventory', category='consumable') }}" class="btn btn-warning">
                    <i class="fas fa-download"></i> 导出消耗品
                </a>
            </div>
        </div>
    </div>
</div>

<style>
.stats-overview {
    padding: 1.5rem;
    background-color: var(--light-color);
    border-bottom: 1px solid var(--border-color);
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.stat-card {
    background: white;
    padding: 1.5rem;
    border-radius: 15px;
    display: flex;
    align-items: center;
    gap: 1rem;
    box-shadow: 0 2px 8px var(--shadow-color);
    border: 1px solid var(--border-color);
}

.stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background-color: var(--primary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-color);
    font-size: 1.5rem;
}

.stat-content h3 {
    margin: 0;
    font-size: 1.8rem;
    font-weight: bold;
    color: var(--text-color);
}

.stat-content p {
    margin: 0;
    color: var(--text-muted);
    font-size: 0.9rem;
}

.category-section {
    padding: 1.5rem;
}

.category-section h3 {
    margin-bottom: 1rem;
    color: var(--text-color);
}

.category-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
}

.category-card {
    background: white;
    padding: 1.5rem;
    border-radius: 15px;
    border: 1px solid var(--border-color);
    box-shadow: 0 2px 8px var(--shadow-color);
}

.category-card h4 {
    margin: 0 0 1rem 0;
    color: var(--text-color);
    font-size: 1.2rem;
}

.category-stats {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.stat-item .label {
    color: var(--text-muted);
    font-size: 0.9rem;
}

.stat-item .value {
    font-weight: 600;
    color: var(--text-color);
}

.export-section {
    padding: 1.5rem;
    border-top: 1px solid var(--border-color);
}

.export-section h3 {
    margin-bottom: 1rem;
    color: var(--text-color);
}

.export-buttons {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.export-buttons .btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}
</style>
{% endblock %}
