#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from services.material_service import MaterialService
from models.user import User

# 创建测试用户
admin_user = User(
    id=21,
    username='admin',
    real_name='系统管理员',
    role='admin',
    department_id=16
)

employee_user = User(
    id=22,
    username='<PERSON><PERSON><PERSON>',
    real_name='张三',
    role='employee',
    department_id=16
)

material_service = MaterialService()

try:
    print("测试管理员获取物资列表:")
    admin_materials = material_service.get_material_list(admin_user)
    print(f"管理员看到的物资数量: {len(admin_materials)}")
    if admin_materials:
        print("前5个物资:")
        for i, material in enumerate(admin_materials[:5]):
            print(f"  {i+1}. {material['name']} - {material['category']}")
    
    print("\n测试员工获取物资列表:")
    employee_materials = material_service.get_material_list(employee_user)
    print(f"员工看到的物资数量: {len(employee_materials)}")
    if employee_materials:
        print("前5个物资:")
        for i, material in enumerate(employee_materials[:5]):
            print(f"  {i+1}. {material['name']} - {material['category']}")
    
except Exception as e:
    print(f"错误: {e}")
    import traceback
    traceback.print_exc()
