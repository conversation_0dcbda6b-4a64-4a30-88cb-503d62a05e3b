from dao.database import db
from models.allocation import MaterialAllocation, MaterialRequest
from datetime import datetime

class AllocationDAO:
    def __init__(self):
        self.db = db
    
    def create_allocation(self, allocation):
        """创建物资分配记录"""
        sql = """
        INSERT INTO material_allocations (material_id, department_id, user_id, allocated_by,
                                        quantity, allocation_date, status, notes)
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
        """
        params = (allocation.material_id, allocation.department_id, allocation.user_id,
                 allocation.allocated_by, allocation.quantity, allocation.allocation_date,
                 allocation.status, allocation.notes)
        allocation_id = self.db.execute_insert(sql, params)
        allocation.id = allocation_id
        return allocation
    
    def get_allocation_by_id(self, allocation_id):
        """根据ID获取分配记录"""
        sql = "SELECT * FROM material_allocations WHERE id = %s"
        result = self.db.execute_query_one(sql, (allocation_id,))
        return MaterialAllocation.from_dict(result) if result else None
    
    def get_allocations_by_department(self, department_id):
        """根据科室获取分配记录"""
        sql = """
        SELECT ma.*, m.name as material_name, m.category, u.real_name as user_name
        FROM material_allocations ma
        LEFT JOIN materials m ON ma.material_id = m.id
        LEFT JOIN users u ON ma.user_id = u.id
        WHERE ma.department_id = %s
        ORDER BY ma.allocation_date DESC
        """
        return self.db.execute_query(sql, (department_id,))
    
    def get_allocations_by_user(self, user_id):
        """根据用户获取分配记录"""
        sql = """
        SELECT ma.*, m.name as material_name, m.category, d.name as department_name
        FROM material_allocations ma
        LEFT JOIN materials m ON ma.material_id = m.id
        LEFT JOIN departments d ON ma.department_id = d.id
        WHERE ma.user_id = %s
        ORDER BY ma.allocation_date DESC
        """
        return self.db.execute_query(sql, (user_id,))
    
    def get_all_allocations(self):
        """获取所有分配记录"""
        sql = """
        SELECT ma.*, m.name as material_name, m.category, 
               d.name as department_name, u.real_name as user_name,
               admin.real_name as allocated_by_name
        FROM material_allocations ma
        LEFT JOIN materials m ON ma.material_id = m.id
        LEFT JOIN departments d ON ma.department_id = d.id
        LEFT JOIN users u ON ma.user_id = u.id
        LEFT JOIN users admin ON ma.allocated_by = admin.id
        ORDER BY ma.allocation_date DESC
        """
        return self.db.execute_query(sql)
    
    def update_allocation_status(self, allocation_id, status, return_date=None):
        """更新分配状态"""
        if return_date:
            sql = """
            UPDATE material_allocations SET status = %s, return_date = %s, updated_at = %s
            WHERE id = %s
            """
            params = (status, return_date, datetime.now(), allocation_id)
        else:
            sql = """
            UPDATE material_allocations SET status = %s, updated_at = %s
            WHERE id = %s
            """
            params = (status, datetime.now(), allocation_id)
        return self.db.execute_update(sql, params)

    def get_material_usage_history(self, material_id, start_date, end_date):
        """获取物资使用历史"""
        sql = """
        SELECT allocation_date, quantity
        FROM material_allocations
        WHERE material_id = %s AND allocation_date BETWEEN %s AND %s
        ORDER BY allocation_date
        """
        return self.db.execute_query(sql, (material_id, start_date, end_date))

    def get_department_usage_pattern(self, department_id, start_date, end_date):
        """获取部门使用模式"""
        sql = """
        SELECT DATE(allocation_date) as date, SUM(quantity) as total_quantity
        FROM material_allocations
        WHERE department_id = %s AND allocation_date BETWEEN %s AND %s
        GROUP BY DATE(allocation_date)
        ORDER BY date
        """
        return self.db.execute_query(sql, (department_id, start_date, end_date))

    def get_overall_usage_pattern(self, start_date, end_date):
        """获取整体使用模式"""
        sql = """
        SELECT DATE(allocation_date) as date, SUM(quantity) as total_quantity
        FROM material_allocations
        WHERE allocation_date BETWEEN %s AND %s
        GROUP BY DATE(allocation_date)
        ORDER BY date
        """
        return self.db.execute_query(sql, (start_date, end_date))


class RequestDAO:
    def __init__(self):
        self.db = db
    
    def create_request(self, request):
        """创建物资申请"""
        sql = """
        INSERT INTO material_requests (material_id, user_id, department_id, quantity,
                                     request_date, status, reason)
        VALUES (%s, %s, %s, %s, %s, %s, %s)
        """
        params = (request.material_id, request.user_id, request.department_id,
                 request.quantity, request.request_date, request.status, request.reason)
        request_id = self.db.execute_insert(sql, params)
        request.id = request_id
        return request
    
    def get_request_by_id(self, request_id):
        """根据ID获取申请记录"""
        sql = "SELECT * FROM material_requests WHERE id = %s"
        result = self.db.execute_query_one(sql, (request_id,))
        return MaterialRequest.from_dict(result) if result else None
    
    def get_requests_by_user(self, user_id):
        """根据用户获取申请记录"""
        sql = """
        SELECT mr.*, m.name as material_name, m.category, d.name as department_name
        FROM material_requests mr
        LEFT JOIN materials m ON mr.material_id = m.id
        LEFT JOIN departments d ON mr.department_id = d.id
        WHERE mr.user_id = %s
        ORDER BY mr.request_date DESC
        """
        return self.db.execute_query(sql, (user_id,))
    
    def get_pending_requests(self):
        """获取待审核的申请"""
        sql = """
        SELECT mr.*, m.name as material_name, m.category,
               d.name as department_name, u.real_name as user_name
        FROM material_requests mr
        LEFT JOIN materials m ON mr.material_id = m.id
        LEFT JOIN users u ON mr.user_id = u.id
        LEFT JOIN departments d ON u.department_id = d.id
        WHERE mr.status = 'pending'
        ORDER BY mr.request_date ASC
        """
        return self.db.execute_query(sql)
    
    def approve_request(self, request_id, processed_by, notes=None):
        """批准申请"""
        sql = """
        UPDATE material_requests SET status = 'approved', processed_by = %s,
               processed_date = %s, notes = %s, updated_at = %s
        WHERE id = %s
        """
        params = (processed_by, datetime.now().date(), notes, datetime.now(), request_id)
        return self.db.execute_update(sql, params)
    
    def reject_request(self, request_id, processed_by, notes=None):
        """拒绝申请"""
        sql = """
        UPDATE material_requests SET status = 'rejected', processed_by = %s,
               processed_date = %s, notes = %s, updated_at = %s
        WHERE id = %s
        """
        params = (processed_by, datetime.now().date(), notes, datetime.now(), request_id)
        return self.db.execute_update(sql, params)

    def get_material_average_quantity(self, material_id):
        """获取物资的平均申请数量"""
        sql = """
        SELECT AVG(quantity) as avg_quantity
        FROM material_requests
        WHERE material_id = %s AND status IN ('approved', 'allocated')
        """
        result = self.db.execute_query_one(sql, (material_id,))
        return result['avg_quantity'] if result and result['avg_quantity'] else 1

    def get_user_recent_requests(self, user_id, days=30):
        """获取用户最近的申请记录"""
        sql = """
        SELECT mr.*, m.name as material_name
        FROM material_requests mr
        LEFT JOIN materials m ON mr.material_id = m.id
        WHERE mr.user_id = %s AND mr.request_date >= DATE_SUB(NOW(), INTERVAL %s DAY)
        ORDER BY mr.request_date DESC
        """
        return self.db.execute_query(sql, (user_id, days))

    def get_user_request_statistics(self, user_id):
        """获取用户申请统计"""
        sql = """
        SELECT
            COUNT(*) as total_requests,
            SUM(CASE WHEN status = 'approved' THEN 1 ELSE 0 END) as approved_requests,
            SUM(CASE WHEN status = 'rejected' THEN 1 ELSE 0 END) as rejected_requests
        FROM material_requests
        WHERE user_id = %s
        """
        result = self.db.execute_query_one(sql, (user_id,))
        return result if result else {'total_requests': 0, 'approved_requests': 0, 'rejected_requests': 0}

    def get_department_material_statistics(self, department_id, material_id):
        """获取部门物资申请统计"""
        sql = """
        SELECT
            COUNT(*) as total_requests,
            AVG(quantity) as avg_quantity
        FROM material_requests mr
        JOIN users u ON mr.user_id = u.id
        WHERE u.department_id = %s AND mr.material_id = %s
        """
        result = self.db.execute_query_one(sql, (department_id, material_id))
        return result if result else {'total_requests': 0, 'avg_quantity': 0}

    def get_pending_requests_for_material(self, material_id):
        """获取特定物资的待审核申请"""
        sql = """
        SELECT mr.*, u.real_name as user_name, d.name as department_name
        FROM material_requests mr
        LEFT JOIN users u ON mr.user_id = u.id
        LEFT JOIN departments d ON u.department_id = d.id
        WHERE mr.material_id = %s AND mr.status = 'pending'
        ORDER BY mr.request_date ASC
        """
        return self.db.execute_query(sql, (material_id,))
