#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DeepSeek API服务
提供与DeepSeek AI模型的交互功能
"""

import requests
import json
import logging
from typing import Dict, List, Any, Optional
from config import Config

class DeepSeekService:
    """DeepSeek API服务类"""
    
    def __init__(self):
        self.api_key = Config.DEEPSEEK_API_KEY
        self.base_url = Config.DEEPSEEK_BASE_URL
        self.model = Config.DEEPSEEK_MODEL
        self.logger = logging.getLogger(__name__)
        
        # 设置请求头
        self.headers = {
            'Authorization': f'Bearer {self.api_key}',
            'Content-Type': 'application/json'
        }
    
    def chat_completion(self, messages: List[Dict[str, str]], 
                       temperature: float = 0.7,
                       max_tokens: int = 1000) -> Optional[str]:
        """
        调用DeepSeek聊天完成API
        
        Args:
            messages: 消息列表，格式为 [{"role": "user", "content": "消息内容"}]
            temperature: 温度参数，控制回复的随机性
            max_tokens: 最大token数量
            
        Returns:
            AI回复的文本内容
        """
        try:
            url = f"{self.base_url}/chat/completions"
            
            payload = {
                "model": self.model,
                "messages": messages,
                "temperature": temperature,
                "max_tokens": max_tokens,
                "stream": False
            }
            
            response = requests.post(url, headers=self.headers, json=payload, timeout=30)
            response.raise_for_status()
            
            result = response.json()
            
            if 'choices' in result and len(result['choices']) > 0:
                return result['choices'][0]['message']['content']
            else:
                self.logger.error(f"DeepSeek API返回格式异常: {result}")
                return None
                
        except requests.exceptions.RequestException as e:
            self.logger.error(f"DeepSeek API请求失败: {e}")
            return None
        except Exception as e:
            self.logger.error(f"DeepSeek API调用异常: {e}")
            return None
    
    def analyze_material_request(self, request_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        分析物资申请的合理性
        
        Args:
            request_data: 申请数据
            
        Returns:
            分析结果
        """
        try:
            prompt = f"""
作为一个物资管理专家，请分析以下物资申请的合理性：

申请信息：
- 物资名称：{request_data.get('material_name', '未知')}
- 申请数量：{request_data.get('quantity', 0)}
- 申请理由：{request_data.get('reason', '无')}
- 申请人部门：{request_data.get('department_name', '未知')}
- 申请人：{request_data.get('user_name', '未知')}

请从以下几个方面进行分析：
1. 申请数量是否合理
2. 申请理由是否充分
3. 是否存在潜在风险
4. 审批建议（批准/拒绝/需要更多信息）

请以JSON格式返回分析结果，包含：
- recommendation: "approve" | "reject" | "review"
- confidence: 0-1之间的置信度
- reasons: 分析理由列表
- risk_level: "low" | "medium" | "high"
- suggestions: 改进建议列表
"""

            messages = [{"role": "user", "content": prompt}]
            response = self.chat_completion(messages, temperature=0.3)
            
            if response:
                try:
                    # 尝试解析JSON响应
                    result = json.loads(response)
                    return {
                        'success': True,
                        'data': result,
                        'raw_response': response
                    }
                except json.JSONDecodeError:
                    # 如果不是JSON格式，返回原始文本
                    return {
                        'success': True,
                        'data': {
                            'recommendation': 'review',
                            'confidence': 0.5,
                            'reasons': [response],
                            'risk_level': 'medium',
                            'suggestions': ['需要人工审核']
                        },
                        'raw_response': response
                    }
            else:
                return {
                    'success': False,
                    'error': 'DeepSeek API调用失败'
                }
                
        except Exception as e:
            self.logger.error(f"分析物资申请失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def suggest_materials(self, user_context: Dict[str, Any]) -> Dict[str, Any]:
        """
        根据用户上下文推荐物资
        
        Args:
            user_context: 用户上下文信息
            
        Returns:
            推荐结果
        """
        try:
            prompt = f"""
作为一个物资管理助手，请根据以下信息为用户推荐合适的物资：

用户信息：
- 部门：{user_context.get('department_name', '未知')}
- 职位：{user_context.get('role', '未知')}
- 需求类别：{user_context.get('category', '办公用品')}
- 历史申请：{user_context.get('history', '无')}

请推荐3-5个最适合的物资，并说明推荐理由。

请以JSON格式返回，包含：
- recommendations: 推荐物资列表，每个包含name, reason, priority
- category_suggestions: 类别建议
- tips: 使用建议
"""

            messages = [{"role": "user", "content": prompt}]
            response = self.chat_completion(messages, temperature=0.7)
            
            if response:
                try:
                    result = json.loads(response)
                    return {
                        'success': True,
                        'data': result,
                        'raw_response': response
                    }
                except json.JSONDecodeError:
                    return {
                        'success': True,
                        'data': {
                            'recommendations': [
                                {'name': '办公用品套装', 'reason': response[:100], 'priority': 'medium'}
                            ],
                            'category_suggestions': ['办公用品'],
                            'tips': [response]
                        },
                        'raw_response': response
                    }
            else:
                return {
                    'success': False,
                    'error': 'DeepSeek API调用失败'
                }
                
        except Exception as e:
            self.logger.error(f"推荐物资失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def analyze_inventory_trends(self, inventory_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        分析库存趋势
        
        Args:
            inventory_data: 库存数据
            
        Returns:
            趋势分析结果
        """
        try:
            prompt = f"""
作为库存管理专家，请分析以下库存数据的趋势：

库存统计：
- 总物资数：{inventory_data.get('total_materials', 0)}
- 低库存物资：{inventory_data.get('low_stock_count', 0)}
- 紧急补货物资：{inventory_data.get('critical_count', 0)}
- 近期使用趋势：{inventory_data.get('usage_trend', '无数据')}

请分析：
1. 当前库存状况
2. 潜在风险
3. 补货建议
4. 优化策略

请以JSON格式返回分析结果。
"""

            messages = [{"role": "user", "content": prompt}]
            response = self.chat_completion(messages, temperature=0.5)
            
            if response:
                try:
                    result = json.loads(response)
                    return {
                        'success': True,
                        'data': result,
                        'raw_response': response
                    }
                except json.JSONDecodeError:
                    return {
                        'success': True,
                        'data': {
                            'status': 'analyzed',
                            'insights': [response],
                            'recommendations': ['需要进一步分析'],
                            'risk_level': 'medium'
                        },
                        'raw_response': response
                    }
            else:
                return {
                    'success': False,
                    'error': 'DeepSeek API调用失败'
                }
                
        except Exception as e:
            self.logger.error(f"分析库存趋势失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def generate_report_insights(self, report_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        生成报告洞察
        
        Args:
            report_data: 报告数据
            
        Returns:
            洞察结果
        """
        try:
            prompt = f"""
作为数据分析专家，请分析以下报告数据并提供洞察：

报告数据：
{json.dumps(report_data, ensure_ascii=False, indent=2)}

请提供：
1. 关键发现
2. 趋势分析
3. 异常识别
4. 改进建议

请以JSON格式返回分析结果。
"""

            messages = [{"role": "user", "content": prompt}]
            response = self.chat_completion(messages, temperature=0.6)
            
            if response:
                try:
                    result = json.loads(response)
                    return {
                        'success': True,
                        'data': result,
                        'raw_response': response
                    }
                except json.JSONDecodeError:
                    return {
                        'success': True,
                        'data': {
                            'key_findings': [response[:200]],
                            'trends': ['需要进一步分析'],
                            'anomalies': [],
                            'recommendations': ['持续监控']
                        },
                        'raw_response': response
                    }
            else:
                return {
                    'success': False,
                    'error': 'DeepSeek API调用失败'
                }
                
        except Exception as e:
            self.logger.error(f"生成报告洞察失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def chat_with_user(self, message: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        与用户进行对话
        
        Args:
            message: 用户消息
            context: 对话上下文
            
        Returns:
            AI回复
        """
        try:
            system_prompt = """
你是一个专业的物资管理助手，负责帮助用户处理物资申请、库存管理、数据分析等任务。
请用友好、专业的语气回答用户问题，并提供实用的建议。
如果用户询问具体的操作步骤，请提供详细的指导。
"""

            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": message}
            ]
            
            # 如果有上下文，添加到消息中
            if context:
                context_info = f"用户上下文：{json.dumps(context, ensure_ascii=False)}"
                messages.insert(1, {"role": "system", "content": context_info})
            
            response = self.chat_completion(messages, temperature=0.8)
            
            if response:
                return {
                    'success': True,
                    'data': {
                        'response': response,
                        'agent_name': '智能物资助手'
                    }
                }
            else:
                return {
                    'success': False,
                    'error': 'DeepSeek API调用失败'
                }
                
        except Exception as e:
            self.logger.error(f"用户对话失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }

# 创建全局实例
deepseek_service = DeepSeekService()
