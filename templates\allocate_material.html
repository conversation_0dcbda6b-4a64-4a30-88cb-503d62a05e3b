{% extends "base.html" %}

{% block title %}分配物资 - 金融企业物资管理系统{% endblock %}

{% block content %}
<div class="card">
    <div class="card-header">
        <h1 class="card-title">分配物资</h1>
        <a href="{{ url_for('material.material_detail', material_id=material_id) }}" class="btn btn-secondary">返回详情</a>
    </div>
    
    <form method="POST">
        <div class="form-group">
            <label for="department_id" class="form-label">目标科室 *</label>
            <select id="department_id" name="department_id" class="form-control form-select" required>
                <option value="">请选择科室</option>
                {% for dept in departments %}
                <option value="{{ dept.id }}">{{ dept.name }}</option>
                {% endfor %}
            </select>
        </div>
        
        <div class="form-group">
            <label for="user_id" class="form-label">指定用户（可选）</label>
            <select id="user_id" name="user_id" class="form-control form-select">
                <option value="">请选择用户（可选）</option>
                {% for user in users %}
                <option value="{{ user.id }}" data-department="{{ user.department_id }}">
                    {{ user.real_name }} ({{ user.username }})
                </option>
                {% endfor %}
            </select>
        </div>
        
        <div class="form-group">
            <label for="quantity" class="form-label">分配数量 *</label>
            <input type="number" id="quantity" name="quantity" class="form-control" min="1" value="1" required>
        </div>
        
        <div class="form-group">
            <label for="notes" class="form-label">备注</label>
            <textarea id="notes" name="notes" class="form-control" rows="3"></textarea>
        </div>
        
        <div class="d-flex gap-2">
            <button type="submit" class="btn btn-success">确认分配</button>
            <a href="{{ url_for('material.material_detail', material_id=material_id) }}" class="btn btn-secondary">取消</a>
        </div>
    </form>
</div>

<script>
// 根据选择的科室过滤用户
document.getElementById('department_id').addEventListener('change', function() {
    const selectedDeptId = this.value;
    const userSelect = document.getElementById('user_id');
    const userOptions = userSelect.querySelectorAll('option');
    
    userOptions.forEach(option => {
        if (option.value === '') {
            option.style.display = 'block';
            return;
        }
        
        const userDeptId = option.getAttribute('data-department');
        if (!selectedDeptId || userDeptId === selectedDeptId) {
            option.style.display = 'block';
        } else {
            option.style.display = 'none';
        }
    });
    
    // 重置用户选择
    userSelect.value = '';
});
</script>
{% endblock %}
