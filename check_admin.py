#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查管理员账号
"""

import pymysql
import hashlib

def check_admin():
    """检查管理员账号"""
    config = {
        'host': 'localhost',
        'user': 'root',
        'password': 'qyf20031211',
        'database': 'goods',
        'charset': 'utf8mb4'
    }
    
    try:
        connection = pymysql.connect(**config)
        cursor = connection.cursor()
        
        # 查询管理员账号
        cursor.execute("SELECT username, password_hash FROM users WHERE username = 'admin'")
        result = cursor.fetchone()
        
        if result:
            username, stored_hash = result
            print(f"用户名: {username}")
            print(f"存储的密码哈希: {stored_hash}")
            
            # 计算admin123的哈希
            expected_hash = hashlib.sha256('admin123'.encode()).hexdigest()
            print(f"期望的密码哈希: {expected_hash}")
            
            if stored_hash == expected_hash:
                print("✅ 密码哈希匹配")
            else:
                print("❌ 密码哈希不匹配，更新密码哈希...")
                cursor.execute("UPDATE users SET password_hash = %s WHERE username = 'admin'", (expected_hash,))
                connection.commit()
                print("✅ 密码哈希已更新")
        else:
            print("❌ 未找到管理员账号")
            
    except Exception as e:
        print(f"❌ 错误: {e}")
    finally:
        if 'connection' in locals():
            connection.close()

if __name__ == '__main__':
    check_admin()
