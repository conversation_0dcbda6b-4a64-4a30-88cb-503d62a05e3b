{% extends "base.html" %}

{% block title %}高级统计查询 - 金融企业物资管理系统{% endblock %}

{% block extra_css %}
<style>
/* 查询表单样式 */
.query-form {
    background: white;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-bottom: 20px;
}

.form-row {
    display: flex;
    gap: 15px;
    align-items: end;
    flex-wrap: wrap;
    margin-bottom: 15px;
}

.form-group {
    flex: 1;
    min-width: 200px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
    color: var(--text-color);
}

/* 统计卡片样式 */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: white;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    text-align: center;
}

.stat-number {
    font-size: 2rem;
    font-weight: bold;
    color: var(--primary-color);
    margin-bottom: 5px;
}

.stat-label {
    color: var(--text-color);
    font-size: 0.9rem;
}

/* 科室统计表格 */
.department-stats {
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    overflow: hidden;
    margin-bottom: 20px;
}

.department-stats h3 {
    background: var(--primary-color);
    color: white;
    padding: 15px 20px;
    margin: 0;
    font-size: 1.1rem;
}

.dept-stat-item {
    padding: 15px 20px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.dept-stat-item:last-child {
    border-bottom: none;
}

.dept-name {
    font-weight: 600;
    color: var(--text-color);
}

.dept-numbers {
    display: flex;
    gap: 20px;
    font-size: 0.9rem;
}

.dept-number {
    text-align: center;
}

.dept-number .value {
    font-weight: bold;
    color: var(--primary-color);
    display: block;
}

.dept-number .label {
    color: var(--dark-color);
    font-size: 0.8rem;
}
</style>
{% endblock %}

{% block content %}
<div class="card">
    <div class="card-header">
        <h1 class="card-title">高级统计查询</h1>
        <p>按物资类别、科室、时间等多维度查询和统计物资信息</p>
    </div>
    
    <!-- 查询表单 -->
    <div class="query-form">
        <form method="GET">
            <div class="form-row">
                <div class="form-group">
                    <label for="category">物资类别</label>
                    <select name="category" id="category" class="form-control">
                        <option value="">全部类别</option>
                        <option value="fixed_asset" {% if filters.category == 'fixed_asset' %}selected{% endif %}>固定资产</option>
                        <option value="consumable" {% if filters.category == 'consumable' %}selected{% endif %}>耗材</option>
                    </select>
                </div>
                
                {% if user.role == 'admin' and departments %}
                <div class="form-group">
                    <label for="department_id">科室</label>
                    <select name="department_id" id="department_id" class="form-control">
                        <option value="">全部科室</option>
                        {% for dept in departments %}
                        <option value="{{ dept.id }}" {% if filters.department_id == dept.id %}selected{% endif %}>{{ dept.name }}</option>
                        {% endfor %}
                    </select>
                </div>
                {% endif %}
                
                <div class="form-group">
                    <label for="start_date">开始日期</label>
                    <input type="date" name="start_date" id="start_date" class="form-control" 
                           value="{{ filters.start_date or '' }}">
                </div>
                
                <div class="form-group">
                    <label for="end_date">结束日期</label>
                    <input type="date" name="end_date" id="end_date" class="form-control" 
                           value="{{ filters.end_date or '' }}">
                </div>
                
                <div class="form-group">
                    <button type="submit" class="btn btn-primary">查询统计</button>
                    <a href="{{ url_for('report.advanced_statistics') }}" class="btn btn-secondary">重置</a>
                </div>
            </div>
        </form>
    </div>
    
    {% if stats_data %}
    <!-- 总体统计 -->
    {% if stats_data.category_summary %}
    <div class="stats-grid">
        {% for category, data in stats_data.category_summary.items() %}
        <div class="stat-card">
            <div class="stat-number">{{ data.count }}</div>
            <div class="stat-label">
                {% if category == 'fixed_asset' %}固定资产总数{% else %}耗材总数{% endif %}
            </div>
            <div style="margin-top: 10px; font-size: 0.9rem; color: var(--dark-color);">
                已分配：{{ data.allocated }} | 价值：¥{{ "%.2f"|format(data.value) }}
            </div>
        </div>
        {% endfor %}
        
        <div class="stat-card">
            <div class="stat-number">{{ stats_data.total_departments }}</div>
            <div class="stat-label">涉及科室数量</div>
        </div>
    </div>
    {% endif %}
    
    <!-- 科室分配统计 -->
    {% if stats_data.department_allocations %}
    <div class="department-stats">
        <h3>科室分配统计</h3>
        {% for dept_name, dept_data in stats_data.department_allocations.items() %}
        <div class="dept-stat-item">
            <div class="dept-name">{{ dept_name }}</div>
            <div class="dept-numbers">
                <div class="dept-number">
                    <span class="value">{{ dept_data.total_allocations }}</span>
                    <span class="label">总分配</span>
                </div>
                <div class="dept-number">
                    <span class="value">{{ dept_data.fixed_assets }}</span>
                    <span class="label">固定资产</span>
                </div>
                <div class="dept-number">
                    <span class="value">{{ dept_data.consumables }}</span>
                    <span class="label">耗材</span>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
    {% endif %}
    
    {% else %}
    <div class="text-center py-5">
        <i class="fas fa-chart-bar fa-3x text-muted mb-3"></i>
        <h4 class="text-muted">请设置查询条件</h4>
        <p class="text-muted">选择物资类别、科室或时间范围来查看统计数据</p>
    </div>
    {% endif %}
</div>
{% endblock %}
