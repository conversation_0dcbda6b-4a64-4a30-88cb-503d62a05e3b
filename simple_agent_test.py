#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的智能体测试脚本
"""

def test_basic_functionality():
    """测试基本功能"""
    print("=== 智能体系统基本功能测试 ===")
    
    try:
        # 测试导入
        print("1. 测试导入智能体模块...")
        from agents.request_assistant_agent import RequestAssistantAgent
        from agents.approval_assistant_agent import ApprovalAssistantAgent
        from agents.inventory_manager_agent import InventoryManagerAgent
        from agents.report_analyst_agent import ReportAnalystAgent
        from agents.agent_coordinator import AgentCoordinator
        print("   ✓ 所有智能体模块导入成功")
        
        # 测试智能体创建
        print("2. 测试智能体创建...")
        request_agent = RequestAssistantAgent()
        approval_agent = ApprovalAssistantAgent()
        inventory_agent = InventoryManagerAgent()
        report_agent = ReportAnalystAgent()
        coordinator = AgentCoordinator()
        print("   ✓ 所有智能体创建成功")
        
        # 测试能力获取
        print("3. 测试智能体能力...")
        print(f"   申请助手能力: {request_agent.get_capabilities()}")
        print(f"   审批助手能力: {approval_agent.get_capabilities()}")
        print(f"   库存管理能力: {inventory_agent.get_capabilities()}")
        print(f"   报表分析能力: {report_agent.get_capabilities()}")
        print(f"   协调中心能力: {coordinator.get_capabilities()}")
        
        # 测试健康检查
        print("4. 测试健康检查...")
        health_result = request_agent.health_check()
        print(f"   申请助手健康状态: {health_result['success']}")
        
        # 测试基本处理功能
        print("5. 测试基本处理功能...")
        
        # 测试申请助手
        result = request_agent.process({
            'action': 'suggest_materials',
            'user_id': 1,
            'department_id': 1,
            'category': '办公用品'
        })
        print(f"   申请助手测试: {'成功' if result.get('success') else '失败'}")
        
        # 测试库存管理
        result = inventory_agent.process({
            'action': 'monitor_inventory'
        })
        print(f"   库存管理测试: {'成功' if result.get('success') else '失败'}")
        
        # 测试协调中心
        result = coordinator.process({
            'action': 'agent_status'
        })
        print(f"   协调中心测试: {'成功' if result.get('success') else '失败'}")
        
        print("\n=== 基本功能测试完成 ===")
        print("✓ 智能体系统运行正常")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_database_connection():
    """测试数据库连接"""
    print("\n=== 数据库连接测试 ===")
    
    try:
        from dao.database import db
        
        # 测试数据库连接
        result = db.execute_query("SELECT 1 as test")
        if result:
            print("✓ 数据库连接正常")
            
            # 测试基本查询
            materials = db.execute_query("SELECT COUNT(*) as count FROM materials")
            if materials:
                print(f"✓ 物资表查询正常，共有 {materials[0]['count']} 条记录")
            
            users = db.execute_query("SELECT COUNT(*) as count FROM users")
            if users:
                print(f"✓ 用户表查询正常，共有 {users[0]['count']} 条记录")
                
            return True
        else:
            print("❌ 数据库连接失败")
            return False
            
    except Exception as e:
        print(f"❌ 数据库测试失败: {e}")
        return False

def test_web_integration():
    """测试Web集成"""
    print("\n=== Web集成测试 ===")
    
    try:
        from controllers.agent_controller import agent_bp
        print("✓ 智能体控制器导入成功")
        
        from app import create_app
        app = create_app()
        print("✓ Flask应用创建成功")
        
        # 检查路由注册
        routes = [str(rule) for rule in app.url_map.iter_rules()]
        agent_routes = [route for route in routes if '/agent' in route]
        
        if agent_routes:
            print(f"✓ 智能体路由注册成功，共 {len(agent_routes)} 个路由")
            print("   主要路由:")
            for route in agent_routes[:5]:  # 显示前5个路由
                print(f"     {route}")
        else:
            print("❌ 智能体路由注册失败")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ Web集成测试失败: {e}")
        return False

if __name__ == '__main__':
    print("开始智能体系统测试...\n")
    
    # 运行所有测试
    tests = [
        test_basic_functionality,
        test_database_connection,
        test_web_integration
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print("=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！智能体系统已成功集成到物资管理系统中。")
        print("\n您现在可以:")
        print("1. 启动Flask应用: python app.py")
        print("2. 访问智能体控制台: http://localhost:5000/agent/dashboard")
        print("3. 使用各种智能体功能进行物资管理")
    else:
        print("⚠️  部分测试失败，请检查相关配置。")
