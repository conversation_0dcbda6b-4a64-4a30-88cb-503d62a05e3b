<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}金融企业物资管理系统{% endblock %}</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    {% block extra_css %}{% endblock %}
</head>
<body>
    {% if session.user_id %}
    <nav class="navbar">
        <div class="container">
            <a href="{{ url_for('material.dashboard') }}" class="navbar-brand">
                物资管理系统
            </a>
            
            <ul class="navbar-nav">
                <li><a href="{{ url_for('material.dashboard') }}">仪表板</a></li>
                <li><a href="{{ url_for('material.material_list') }}">物资管理</a></li>
                {% if session.role == 'admin' %}
                <li><a href="{{ url_for('material.add_material') }}">添加物资</a></li>
                <li><a href="{{ url_for('material.request_list') }}">申请审核</a></li>
                {% endif %}
                <li><a href="{{ url_for('material.allocation_history') }}">分配记录</a></li>
                <li><a href="{{ url_for('report.report_dashboard') }}">统计报表</a></li>
                <li><a href="{{ url_for('report.advanced_statistics') }}">高级统计</a></li>
            </ul>
            
            <div class="user-info">
                <span>欢迎，{{ session.real_name }}</span>
                <a href="{{ url_for('auth.profile') }}" class="btn btn-sm">个人信息</a>
                <a href="{{ url_for('auth.logout') }}" class="btn btn-sm">退出</a>
            </div>
        </div>
    </nav>
    {% endif %}

    <main class="container">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'error' if category == 'error' else category }}">
                        {{ message }}
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        {% block content %}{% endblock %}
    </main>

    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
    {% block extra_js %}{% endblock %}
</body>
</html>
