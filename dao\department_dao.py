from dao.database import db
from models.department import Department
from datetime import datetime

class DepartmentDAO:
    def __init__(self):
        self.db = db
    
    def create_department(self, department):
        """创建科室"""
        sql = "INSERT INTO departments (name, description) VALUES (%s, %s)"
        params = (department.name, department.description)
        department_id = self.db.execute_insert(sql, params)
        department.id = department_id
        return department
    
    def get_department_by_id(self, department_id):
        """根据ID获取科室"""
        sql = "SELECT * FROM departments WHERE id = %s"
        result = self.db.execute_query_one(sql, (department_id,))
        return Department.from_dict(result) if result else None
    
    def get_department_by_name(self, name):
        """根据名称获取科室"""
        sql = "SELECT * FROM departments WHERE name = %s"
        result = self.db.execute_query_one(sql, (name,))
        return Department.from_dict(result) if result else None
    
    def get_all_departments(self):
        """获取所有科室"""
        sql = "SELECT * FROM departments ORDER BY name"
        results = self.db.execute_query(sql)
        return [Department.from_dict(row) for row in results]
    
    def update_department(self, department):
        """更新科室信息"""
        sql = """
        UPDATE departments SET name = %s, description = %s, updated_at = %s
        WHERE id = %s
        """
        params = (department.name, department.description, datetime.now(), department.id)
        return self.db.execute_update(sql, params)
    
    def delete_department(self, department_id):
        """删除科室"""
        sql = "DELETE FROM departments WHERE id = %s"
        return self.db.execute_update(sql, (department_id,))
    
    def get_department_statistics(self):
        """获取科室统计信息"""
        sql = """
        SELECT d.id, d.name,
               COUNT(DISTINCT u.id) as user_count,
               COUNT(DISTINCT ma.id) as allocation_count
        FROM departments d
        LEFT JOIN users u ON d.id = u.department_id
        LEFT JOIN material_allocations ma ON d.id = ma.department_id AND ma.status = 'allocated'
        GROUP BY d.id, d.name
        ORDER BY d.name
        """
        return self.db.execute_query(sql)
