<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能体控制台 - 物资管理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .agent-card {
            transition: transform 0.2s;
            cursor: pointer;
        }
        .agent-card:hover {
            transform: translateY(-5px);
        }
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }
        .status-healthy { background-color: #28a745; }
        .status-warning { background-color: #ffc107; }
        .status-error { background-color: #dc3545; }
        .chat-container {
            height: 400px;
            overflow-y: auto;
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            padding: 1rem;
            background-color: #f8f9fa;
        }
        .message {
            margin-bottom: 1rem;
            padding: 0.5rem;
            border-radius: 0.375rem;
        }
        .message.user {
            background-color: #007bff;
            color: white;
            margin-left: 20%;
        }
        .message.agent {
            background-color: white;
            border: 1px solid #dee2e6;
            margin-right: 20%;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="bi bi-robot"></i> 智能体控制台
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/dashboard">返回主控制台</a>
                <a class="nav-link" href="/auth/logout">退出登录</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- 系统状态概览 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-activity"></i> 系统状态
                            <button class="btn btn-sm btn-outline-primary float-end" onclick="refreshSystemStatus()">
                                <i class="bi bi-arrow-clockwise"></i> 刷新
                            </button>
                        </h5>
                    </div>
                    <div class="card-body">
                        <div id="systemStatus" class="row">
                            <div class="col-md-3">
                                <div class="text-center">
                                    <div class="status-indicator status-healthy"></div>
                                    <span>系统正常</span>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center">
                                    <strong>4</strong><br>
                                    <small class="text-muted">活跃智能体</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center">
                                    <strong>0</strong><br>
                                    <small class="text-muted">系统警报</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center">
                                    <strong>98%</strong><br>
                                    <small class="text-muted">系统性能</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 智能体卡片 -->
        <div class="row mb-4">
            <div class="col-md-6 col-lg-3 mb-3">
                <div class="card agent-card h-100" onclick="openAgentPanel('request_assistant')">
                    <div class="card-body text-center">
                        <i class="bi bi-person-check-fill text-primary" style="font-size: 3rem;"></i>
                        <h5 class="card-title mt-3">智能申请助手</h5>
                        <p class="card-text">协助用户智能填写申请、预测需求、优化申请理由</p>
                        <div class="status-indicator status-healthy"></div>
                        <small class="text-muted">运行正常</small>
                    </div>
                </div>
            </div>
            <div class="col-md-6 col-lg-3 mb-3">
                <div class="card agent-card h-100" onclick="openAgentPanel('approval_assistant')">
                    <div class="card-body text-center">
                        <i class="bi bi-clipboard-check text-success" style="font-size: 3rem;"></i>
                        <h5 class="card-title mt-3">智能审批助手</h5>
                        <p class="card-text">自动分析申请合理性、提供审批建议、风险评估</p>
                        <div class="status-indicator status-healthy"></div>
                        <small class="text-muted">运行正常</small>
                    </div>
                </div>
            </div>
            <div class="col-md-6 col-lg-3 mb-3">
                <div class="card agent-card h-100" onclick="openAgentPanel('inventory_manager')">
                    <div class="card-body text-center">
                        <i class="bi bi-boxes text-warning" style="font-size: 3rem;"></i>
                        <h5 class="card-title mt-3">智能库存管理</h5>
                        <p class="card-text">监控库存水平、预测补货需求、优化分配策略</p>
                        <div class="status-indicator status-healthy"></div>
                        <small class="text-muted">运行正常</small>
                    </div>
                </div>
            </div>
            <div class="col-md-6 col-lg-3 mb-3">
                <div class="card agent-card h-100" onclick="openAgentPanel('report_analyst')">
                    <div class="card-body text-center">
                        <i class="bi bi-graph-up text-info" style="font-size: 3rem;"></i>
                        <h5 class="card-title mt-3">智能报表分析</h5>
                        <p class="card-text">自动生成洞察报告、趋势分析、异常检测</p>
                        <div class="status-indicator status-healthy"></div>
                        <small class="text-muted">运行正常</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- 快速操作 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="bi bi-lightning-fill"></i> 快速操作</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3 mb-2">
                                <button class="btn btn-outline-primary w-100" onclick="generateComprehensiveReport()">
                                    <i class="bi bi-file-earmark-text"></i> 生成综合报告
                                </button>
                            </div>
                            <div class="col-md-3 mb-2">
                                <button class="btn btn-outline-success w-100" onclick="checkInventoryStatus()">
                                    <i class="bi bi-box-seam"></i> 检查库存状态
                                </button>
                            </div>
                            <div class="col-md-3 mb-2">
                                <button class="btn btn-outline-warning w-100" onclick="detectAnomalies()">
                                    <i class="bi bi-exclamation-triangle"></i> 异常检测
                                </button>
                            </div>
                            <div class="col-md-3 mb-2">
                                <button class="btn btn-outline-info w-100" onclick="optimizeWorkflow()">
                                    <i class="bi bi-gear"></i> 优化工作流程
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 智能对话 -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="bi bi-chat-dots"></i> 智能对话</h5>
                    </div>
                    <div class="card-body">
                        <div id="chatContainer" class="chat-container mb-3">
                            <div class="message agent">
                                <strong>智能助手:</strong> 您好！我是智能物资管理助手，可以帮助您处理申请、分析数据、监控库存等。请告诉我您需要什么帮助？
                            </div>
                        </div>
                        <div class="input-group">
                            <input type="text" id="chatInput" class="form-control" placeholder="输入您的问题..." onkeypress="handleChatKeyPress(event)">
                            <button class="btn btn-primary" onclick="sendChatMessage()">
                                <i class="bi bi-send"></i> 发送
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 智能体详情模态框 -->
    <div class="modal fade" id="agentModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="agentModalTitle">智能体详情</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="agentModalBody">
                    <!-- 智能体详情内容 -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 刷新系统状态
        function refreshSystemStatus() {
            fetch('/agent/health')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        updateSystemStatusDisplay(data.data);
                    }
                })
                .catch(error => console.error('Error:', error));
        }

        // 更新系统状态显示
        function updateSystemStatusDisplay(statusData) {
            // 更新状态显示逻辑
            console.log('System status:', statusData);
        }

        // 打开智能体面板
        function openAgentPanel(agentType) {
            const agentNames = {
                'request_assistant': '智能申请助手',
                'approval_assistant': '智能审批助手',
                'inventory_manager': '智能库存管理',
                'report_analyst': '智能报表分析'
            };

            document.getElementById('agentModalTitle').textContent = agentNames[agentType];
            document.getElementById('agentModalBody').innerHTML = `
                <p>正在加载 ${agentNames[agentType]} 的详细信息...</p>
                <div class="text-center">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>
            `;

            const modal = new bootstrap.Modal(document.getElementById('agentModal'));
            modal.show();

            // 加载智能体状态
            fetch('/agent/status')
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.data[agentType]) {
                        const agentData = data.data[agentType];
                        document.getElementById('agentModalBody').innerHTML = `
                            <h6>基本信息</h6>
                            <ul class="list-unstyled">
                                <li><strong>名称:</strong> ${agentData.name}</li>
                                <li><strong>描述:</strong> ${agentData.description}</li>
                                <li><strong>状态:</strong> <span class="badge bg-success">${agentData.status}</span></li>
                                <li><strong>最后活动:</strong> ${agentData.last_activity || '无'}</li>
                            </ul>
                            <h6>能力列表</h6>
                            <ul>
                                ${agentData.capabilities.map(cap => `<li>${cap}</li>`).join('')}
                            </ul>
                        `;
                    }
                })
                .catch(error => {
                    document.getElementById('agentModalBody').innerHTML = `
                        <div class="alert alert-danger">
                            加载智能体信息失败: ${error.message}
                        </div>
                    `;
                });
        }

        // 生成综合报告
        function generateComprehensiveReport() {
            fetch('/agent/report/comprehensive')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('综合报告生成成功！');
                        console.log('Report data:', data.data);
                    } else {
                        alert('生成报告失败: ' + data.message);
                    }
                })
                .catch(error => alert('请求失败: ' + error.message));
        }

        // 检查库存状态
        function checkInventoryStatus() {
            fetch('/agent/inventory/monitor')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const summary = data.data.summary;
                        alert(`库存检查完成！\n总物资: ${summary.total_materials}\n紧急: ${summary.critical_count}\n偏低: ${summary.low_count}`);
                    } else {
                        alert('库存检查失败: ' + data.message);
                    }
                })
                .catch(error => alert('请求失败: ' + error.message));
        }

        // 异常检测
        function detectAnomalies() {
            fetch('/agent/report/anomalies')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const summary = data.data.summary;
                        alert(`异常检测完成！\n发现异常: ${summary.total_anomalies}个\n高风险: ${summary.severity_distribution.high}个`);
                    } else {
                        alert('异常检测失败: ' + data.message);
                    }
                })
                .catch(error => alert('请求失败: ' + error.message));
        }

        // 优化工作流程
        function optimizeWorkflow() {
            fetch('/agent/workflow/optimize')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('工作流程优化分析完成！');
                        console.log('Optimization data:', data.data);
                    } else {
                        alert('工作流程优化失败: ' + data.message);
                    }
                })
                .catch(error => alert('请求失败: ' + error.message));
        }

        // 发送聊天消息
        function sendChatMessage() {
            const input = document.getElementById('chatInput');
            const message = input.value.trim();
            if (!message) return;

            // 添加用户消息
            addChatMessage('user', message);
            input.value = '';

            // 发送到智能体
            fetch('/agent/chat', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ message: message })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    addChatMessage('agent', data.data.response, data.data.agent_name);
                } else {
                    addChatMessage('agent', '抱歉，处理您的消息时出现错误: ' + data.error);
                }
            })
            .catch(error => {
                addChatMessage('agent', '网络错误，请稍后重试');
            });
        }

        // 添加聊天消息
        function addChatMessage(type, message, agentName = '智能助手') {
            const container = document.getElementById('chatContainer');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;
            
            if (type === 'user') {
                messageDiv.innerHTML = `<strong>您:</strong> ${message}`;
            } else {
                messageDiv.innerHTML = `<strong>${agentName}:</strong> ${message}`;
            }
            
            container.appendChild(messageDiv);
            container.scrollTop = container.scrollHeight;
        }

        // 处理聊天输入框回车事件
        function handleChatKeyPress(event) {
            if (event.key === 'Enter') {
                sendChatMessage();
            }
        }

        // 页面加载完成后刷新状态
        document.addEventListener('DOMContentLoaded', function() {
            refreshSystemStatus();
        });
    </script>
</body>
</html>
