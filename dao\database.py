import pymysql
from config import Config
import logging

class Database:
    _instance = None
    _connection = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(Database, cls).__new__(cls)
        return cls._instance
    
    def get_connection(self):
        """获取数据库连接"""
        try:
            if self._connection is None or not self._connection.open:
                self._connection = pymysql.connect(**Config.DB_CONFIG)
            return self._connection
        except Exception as e:
            logging.error(f"数据库连接失败: {e}")
            raise
    
    def execute_query(self, sql, params=None):
        """执行查询语句"""
        connection = self.get_connection()
        try:
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                cursor.execute(sql, params)
                return cursor.fetchall()
        except Exception as e:
            logging.error(f"查询执行失败: {e}")
            raise
    
    def execute_query_one(self, sql, params=None):
        """执行查询语句，返回单条记录"""
        connection = self.get_connection()
        try:
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                cursor.execute(sql, params)
                return cursor.fetchone()
        except Exception as e:
            logging.error(f"查询执行失败: {e}")
            raise
    
    def execute_update(self, sql, params=None):
        """执行更新语句"""
        connection = self.get_connection()
        try:
            with connection.cursor() as cursor:
                result = cursor.execute(sql, params)
                connection.commit()
                return result
        except Exception as e:
            connection.rollback()
            logging.error(f"更新执行失败: {e}")
            raise
    
    def execute_insert(self, sql, params=None):
        """执行插入语句，返回插入的ID"""
        connection = self.get_connection()
        try:
            with connection.cursor() as cursor:
                cursor.execute(sql, params)
                connection.commit()
                return cursor.lastrowid
        except Exception as e:
            connection.rollback()
            logging.error(f"插入执行失败: {e}")
            raise
    
    def close(self):
        """关闭数据库连接"""
        if self._connection:
            self._connection.close()
            self._connection = None

# 全局数据库实例
db = Database()
