#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库初始化脚本
"""

import pymysql
from datetime import datetime, date
import hashlib

def hash_password(password):
    """密码哈希"""
    return hashlib.sha256(password.encode()).hexdigest()

def init_database():
    """初始化数据库"""
    # 数据库配置
    config = {
        'host': 'localhost',
        'user': 'root',
        'password': 'lax217652',
        'charset': 'utf8mb4'
    }
    
    try:
        # 连接MySQL服务器
        connection = pymysql.connect(**config)
        cursor = connection.cursor()
        
        # 创建数据库
        print("创建数据库...")
        cursor.execute("CREATE DATABASE IF NOT EXISTS goods CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
        cursor.execute("USE goods")
        
        # 创建科室表
        print("创建科室表...")
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS departments (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(100) NOT NULL UNIQUE,
            description TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )
        """)
        
        # 创建用户表
        print("创建用户表...")
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS users (
            id INT AUTO_INCREMENT PRIMARY KEY,
            username VARCHAR(50) NOT NULL UNIQUE,
            password VARCHAR(255) NOT NULL,
            real_name VARCHAR(100) NOT NULL,
            role ENUM('admin', 'employee') DEFAULT 'employee',
            department_id INT,
            status ENUM('active', 'inactive') DEFAULT 'active',
            last_login TIMESTAMP NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (department_id) REFERENCES departments(id)
        )
        """)
        
        # 创建物资表
        print("创建物资表...")
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS materials (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(200) NOT NULL,
            model VARCHAR(100),
            category ENUM('fixed_asset', 'consumable') NOT NULL,
            asset_number VARCHAR(50) UNIQUE,
            price DECIMAL(10,2) NOT NULL,
            quantity INT NOT NULL DEFAULT 1,
            remaining_quantity INT NOT NULL DEFAULT 1,
            status ENUM('available', 'in_use', 'scrapped') DEFAULT 'available',
            purchase_date DATE,
            supplier VARCHAR(200),
            purchase_amount DECIMAL(10,2),
            description TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_category (category),
            INDEX idx_status (status),
            INDEX idx_asset_number (asset_number)
        )
        """)
        
        # 创建物资分配表
        print("创建物资分配表...")
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS material_allocations (
            id INT AUTO_INCREMENT PRIMARY KEY,
            material_id INT NOT NULL,
            department_id INT NOT NULL,
            user_id INT,
            quantity INT NOT NULL DEFAULT 1,
            allocation_date DATE NOT NULL,
            status ENUM('allocated', 'returned', 'consumed') DEFAULT 'allocated',
            notes TEXT,
            allocated_by INT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (material_id) REFERENCES materials(id),
            FOREIGN KEY (department_id) REFERENCES departments(id),
            FOREIGN KEY (user_id) REFERENCES users(id),
            FOREIGN KEY (allocated_by) REFERENCES users(id),
            INDEX idx_material_id (material_id),
            INDEX idx_department_id (department_id),
            INDEX idx_allocation_date (allocation_date)
        )
        """)
        
        # 创建物资申请表
        print("创建物资申请表...")
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS material_requests (
            id INT AUTO_INCREMENT PRIMARY KEY,
            material_id INT NOT NULL,
            user_id INT NOT NULL,
            department_id INT NOT NULL,
            quantity INT NOT NULL DEFAULT 1,
            reason TEXT,
            status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
            request_date DATE NOT NULL,
            processed_date DATE,
            processed_by INT,
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (material_id) REFERENCES materials(id),
            FOREIGN KEY (user_id) REFERENCES users(id),
            FOREIGN KEY (department_id) REFERENCES departments(id),
            FOREIGN KEY (processed_by) REFERENCES users(id),
            INDEX idx_material_id (material_id),
            INDEX idx_user_id (user_id),
            INDEX idx_status (status)
        )
        """)
        
        # 插入示例数据
        print("插入示例数据...")
        
        # 插入科室数据
        departments = [
            ('财务部', '负责公司财务管理'),
            ('人事部', '负责人力资源管理'),
            ('技术部', '负责技术开发'),
            ('市场部', '负责市场营销'),
            ('行政部', '负责行政管理')
        ]
        
        cursor.execute("SELECT COUNT(*) FROM departments")
        if cursor.fetchone()[0] == 0:
            cursor.executemany(
                "INSERT INTO departments (name, description) VALUES (%s, %s)",
                departments
            )
            connection.commit()  # 提交科室数据
        
        # 获取科室ID
        cursor.execute("SELECT id FROM departments ORDER BY id")
        dept_ids = [row[0] for row in cursor.fetchall()]
        print(f"可用科室ID: {dept_ids}")

        # 插入用户数据
        if dept_ids:
            users = [
                ('admin', '123456', '系统管理员', 'admin', dept_ids[0]),
                ('zhangsan', '123456', '张三', 'employee', dept_ids[0]),
                ('lisi', '123456', '李四', 'employee', dept_ids[1] if len(dept_ids) > 1 else dept_ids[0]),
                ('wangwu', '123456', '王五', 'employee', dept_ids[2] if len(dept_ids) > 2 else dept_ids[0]),
                ('zhaoliu', '123456', '赵六', 'employee', dept_ids[3] if len(dept_ids) > 3 else dept_ids[0])
            ]

            cursor.execute("SELECT COUNT(*) FROM users")
            if cursor.fetchone()[0] == 0:
                cursor.executemany(
                    "INSERT INTO users (username, password, real_name, role, department_id) VALUES (%s, %s, %s, %s, %s)",
                    users
                )
                connection.commit()  # 提交用户数据
        
        # 插入物资数据
        materials = [
            # 固定资产
            ('联想ThinkPad笔记本', 'T14', 'fixed_asset', 5800.00, 8, 6, 'available', '2024-01-15', '联想官方', 46400.00, '办公用笔记本电脑'),
            ('戴尔显示器', 'U2419H', 'fixed_asset', 1200.00, 12, 8, 'available', '2024-01-20', '戴尔官方', 14400.00, '24寸显示器'),
            ('办公椅', 'Herman Miller', 'fixed_asset', 2500.00, 15, 12, 'available', '2024-02-01', '办公家具公司', 37500.00, '人体工学办公椅'),
            ('订书机', '得力', 'fixed_asset', 35.00, 20, 15, 'available', '2024-03-10', '办公用品店', 700.00, '办公订书机'),
            ('台式电脑', 'Dell OptiPlex', 'fixed_asset', 4500.00, 10, 7, 'available', '2024-01-10', '戴尔官方', 45000.00, '办公台式电脑'),
            ('激光打印机', 'HP LaserJet', 'fixed_asset', 2800.00, 6, 4, 'available', '2024-02-15', 'HP官方', 16800.00, '黑白激光打印机'),
            ('办公桌', '宜家BEKANT', 'fixed_asset', 800.00, 25, 20, 'available', '2024-01-25', '宜家', 20000.00, '标准办公桌'),
            ('文件柜', '钢制四层', 'fixed_asset', 600.00, 15, 12, 'available', '2024-02-10', '办公家具厂', 9000.00, '四层文件柜'),
            ('投影仪', 'Epson EB-X41', 'fixed_asset', 3200.00, 4, 3, 'available', '2024-03-01', '爱普生', 12800.00, '会议室投影设备'),
            ('空调', '格力1.5匹', 'fixed_asset', 2800.00, 8, 6, 'available', '2024-01-30', '格力电器', 22400.00, '办公室空调'),
            ('复印机', 'Canon iR2625', 'fixed_asset', 8500.00, 3, 2, 'available', '2024-02-20', '佳能', 25500.00, '多功能复印机'),
            ('会议桌', '实木会议桌', 'fixed_asset', 3500.00, 5, 4, 'available', '2024-01-20', '办公家具厂', 17500.00, '大型会议桌'),
            ('保险柜', '永发YF-D-43', 'fixed_asset', 1200.00, 3, 3, 'available', '2024-02-05', '永发保险柜', 3600.00, '办公保险柜'),

            # 消耗品
            ('A4打印纸', '70g', 'consumable', 25.00, 200, 150, 'available', '2024-03-01', '办公用品店', 5000.00, '办公打印用纸'),
            ('签字笔', '晨光', 'consumable', 2.50, 300, 220, 'available', '2024-03-05', '文具店', 750.00, '黑色签字笔'),
            ('圆珠笔', '晨光', 'consumable', 1.80, 250, 180, 'available', '2024-03-05', '文具店', 450.00, '蓝色圆珠笔'),
            ('铅笔', 'HB', 'consumable', 1.20, 150, 100, 'available', '2024-03-05', '文具店', 180.00, 'HB铅笔'),
            ('橡皮擦', '4B', 'consumable', 0.80, 100, 75, 'available', '2024-03-05', '文具店', 80.00, '4B橡皮擦'),
            ('订书钉', '标准型', 'consumable', 3.50, 80, 60, 'available', '2024-03-10', '办公用品店', 280.00, '标准订书钉'),
            ('胶水', '固体胶', 'consumable', 4.20, 60, 45, 'available', '2024-03-08', '文具店', 252.00, '固体胶棒'),
            ('透明胶带', '18mm', 'consumable', 2.80, 100, 70, 'available', '2024-03-08', '文具店', 280.00, '透明胶带'),
            ('文件夹', 'A4', 'consumable', 3.60, 150, 120, 'available', '2024-03-12', '办公用品店', 540.00, 'A4文件夹'),
            ('便利贴', '彩色', 'consumable', 8.50, 120, 90, 'available', '2024-03-12', '文具店', 1020.00, '彩色便利贴'),
            ('计算器', '科学型', 'consumable', 25.00, 50, 35, 'available', '2024-03-15', '电子产品店', 1250.00, '科学计算器'),
            ('墨盒', 'HP原装', 'consumable', 180.00, 30, 20, 'available', '2024-03-20', 'HP官方', 5400.00, '打印机墨盒'),
            ('硒鼓', 'HP原装', 'consumable', 320.00, 20, 15, 'available', '2024-03-20', 'HP官方', 6400.00, '激光打印机硒鼓'),
            ('复印纸', 'A4 80g', 'consumable', 30.00, 180, 140, 'available', '2024-03-25', '办公用品店', 5400.00, 'A4复印纸'),
            ('档案袋', 'A4牛皮纸', 'consumable', 1.50, 200, 160, 'available', '2024-03-25', '办公用品店', 300.00, 'A4档案袋'),
            ('标签纸', '不干胶', 'consumable', 12.00, 80, 60, 'available', '2024-03-28', '办公用品店', 960.00, '不干胶标签'),
            ('打孔器', '双孔', 'consumable', 15.00, 40, 30, 'available', '2024-03-28', '办公用品店', 600.00, '双孔打孔器'),
            ('夹子', '燕尾夹', 'consumable', 0.50, 300, 250, 'available', '2024-03-30', '文具店', 150.00, '燕尾夹'),
            ('修正液', '涂改液', 'consumable', 3.20, 80, 60, 'available', '2024-03-30', '文具店', 256.00, '涂改液'),

            # 更多固定资产
            ('扫描仪', 'Canon LiDE 300', 'fixed_asset', 680.00, 5, 4, 'available', '2024-04-01', '佳能', 3400.00, '平板扫描仪'),
            ('碎纸机', 'Fellowes 79Ci', 'fixed_asset', 1200.00, 6, 5, 'available', '2024-04-05', '范罗士', 7200.00, '办公碎纸机'),
            ('白板', '磁性白板', 'fixed_asset', 350.00, 8, 7, 'available', '2024-04-10', '办公用品厂', 2800.00, '会议室白板'),
            ('饮水机', '美的立式', 'fixed_asset', 800.00, 4, 3, 'available', '2024-04-12', '美的', 3200.00, '办公饮水机'),
            ('电话机', '松下KX-T7730', 'fixed_asset', 280.00, 15, 12, 'available', '2024-04-15', '松下', 4200.00, '办公电话'),
            ('传真机', 'Brother FAX-2890', 'fixed_asset', 1500.00, 3, 2, 'available', '2024-04-18', '兄弟', 4500.00, '激光传真机'),
            ('路由器', 'TP-Link AC1900', 'fixed_asset', 450.00, 10, 8, 'available', '2024-04-20', 'TP-Link', 4500.00, '无线路由器'),
            ('交换机', 'D-Link DGS-1024D', 'fixed_asset', 320.00, 5, 4, 'available', '2024-04-22', 'D-Link', 1600.00, '24口交换机'),
            ('UPS电源', 'APC BK650M2-CH', 'fixed_asset', 680.00, 8, 6, 'available', '2024-04-25', 'APC', 5440.00, '不间断电源'),
            ('监控摄像头', '海康威视DS-2CD2143G0-I', 'fixed_asset', 580.00, 12, 10, 'available', '2024-04-28', '海康威视', 6960.00, '网络摄像头'),
            ('音响设备', 'JBL Control 25AV', 'fixed_asset', 1200.00, 4, 3, 'available', '2024-05-01', 'JBL', 4800.00, '会议室音响'),
            ('LED显示屏', 'P2.5室内全彩', 'fixed_asset', 15000.00, 2, 2, 'available', '2024-05-05', '联建光电', 30000.00, '大厅显示屏'),
            ('服务器', 'Dell PowerEdge T340', 'fixed_asset', 12000.00, 2, 1, 'available', '2024-05-08', '戴尔', 24000.00, '塔式服务器'),
            ('网络存储', 'Synology DS220+', 'fixed_asset', 2800.00, 3, 2, 'available', '2024-05-10', '群晖', 8400.00, 'NAS存储设备'),

            # 更多消耗品
            ('彩色打印纸', 'A4 80g', 'consumable', 35.00, 100, 80, 'available', '2024-05-12', '办公用品店', 3500.00, '彩色打印纸'),
            ('相片纸', 'A4光面', 'consumable', 45.00, 50, 40, 'available', '2024-05-15', '摄影器材店', 2250.00, '照片打印纸'),
            ('装订夹', '塑料装订夹', 'consumable', 2.20, 200, 150, 'available', '2024-05-18', '文具店', 440.00, '文件装订夹'),
            ('封口胶', '透明封箱胶', 'consumable', 8.50, 80, 60, 'available', '2024-05-20', '包装材料店', 680.00, '封箱胶带'),
            ('标记笔', '马克笔套装', 'consumable', 25.00, 30, 20, 'available', '2024-05-22', '美术用品店', 750.00, '彩色标记笔'),
            ('白板笔', '可擦白板笔', 'consumable', 8.00, 60, 45, 'available', '2024-05-25', '办公用品店', 480.00, '白板专用笔'),
            ('荧光笔', '多色荧光笔', 'consumable', 12.00, 80, 60, 'available', '2024-05-28', '文具店', 960.00, '荧光标记笔'),
            ('回形针', '金属回形针', 'consumable', 5.00, 100, 80, 'available', '2024-05-30', '办公用品店', 500.00, '办公回形针'),
            ('图钉', '彩色图钉', 'consumable', 3.50, 120, 90, 'available', '2024-06-01', '文具店', 420.00, '软木板图钉'),
            ('双面胶', '强力双面胶', 'consumable', 6.80, 90, 70, 'available', '2024-06-03', '胶带厂', 612.00, '双面胶带'),
            ('名片盒', '透明名片盒', 'consumable', 15.00, 40, 30, 'available', '2024-06-05', '办公用品店', 600.00, '名片收纳盒'),
            ('文件袋', '拉链文件袋', 'consumable', 8.50, 100, 75, 'available', '2024-06-08', '办公用品店', 850.00, 'A4文件袋'),
            ('笔筒', '金属笔筒', 'consumable', 18.00, 50, 35, 'available', '2024-06-10', '办公用品店', 900.00, '办公笔筒'),
            ('鼠标垫', '防滑鼠标垫', 'consumable', 12.00, 80, 60, 'available', '2024-06-12', '电脑配件店', 960.00, '电脑鼠标垫'),
            ('键盘膜', '透明键盘膜', 'consumable', 8.00, 60, 45, 'available', '2024-06-15', '电脑配件店', 480.00, '键盘保护膜'),
            ('屏幕清洁剂', '液晶屏清洁套装', 'consumable', 25.00, 40, 30, 'available', '2024-06-18', '电脑配件店', 1000.00, '显示器清洁剂'),
            ('数据线', 'USB-C数据线', 'consumable', 35.00, 50, 40, 'available', '2024-06-20', '电子产品店', 1750.00, 'Type-C充电线'),
            ('网线', 'Cat6网络线', 'consumable', 2.50, 200, 150, 'available', '2024-06-22', '网络设备店', 500.00, '六类网线'),
            ('电池', '5号碱性电池', 'consumable', 3.00, 100, 80, 'available', '2024-06-25', '电子产品店', 300.00, 'AA电池'),
            ('插座', '多功能插座', 'consumable', 45.00, 30, 25, 'available', '2024-06-28', '电器店', 1350.00, '办公插线板')
        ]
        
        cursor.execute("SELECT COUNT(*) FROM materials")
        if cursor.fetchone()[0] == 0:
            for material in materials:
                # 生成资产编号
                if material[2] == 'fixed_asset':  # category
                    cursor.execute("SELECT COUNT(*) FROM materials WHERE category = 'fixed_asset'")
                    count = cursor.fetchone()[0] + 1
                    asset_number = f"FA{datetime.now().strftime('%Y')}{count:04d}"
                else:
                    asset_number = None
                
                cursor.execute("""
                    INSERT INTO materials (name, model, category, asset_number, price, quantity, remaining_quantity,
                                         status, purchase_date, supplier, purchase_amount, description)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """, (material[0], material[1], material[2], asset_number, material[3], material[4],
                     material[5], material[6], material[7], material[8], material[9], material[10]))

        # 插入分配记录数据
        cursor.execute("SELECT COUNT(*) FROM material_allocations")
        if cursor.fetchone()[0] == 0:
            # 获取用户和物资ID
            cursor.execute("SELECT id FROM users WHERE role = 'admin' LIMIT 1")
            admin_result = cursor.fetchone()
            if admin_result:
                admin_id = admin_result[0]

                cursor.execute("SELECT id FROM users WHERE role = 'employee'")
                employee_results = cursor.fetchall()
                employee_ids = [row[0] for row in employee_results] if employee_results else []

                cursor.execute("SELECT id FROM departments")
                department_results = cursor.fetchall()
                department_ids = [row[0] for row in department_results] if department_results else []

                cursor.execute("SELECT id, name, category FROM materials LIMIT 20")
                materials_results = cursor.fetchall()

                if employee_ids and department_ids and materials_results:
                    # 分配记录数据 (material_id, department_id, user_id, quantity, allocation_date, status, notes, allocated_by)
                    allocations = []
                    for i in range(min(20, len(materials_results))):
                        material_id = materials_results[i][0]
                        dept_id = department_ids[i % len(department_ids)]
                        emp_id = employee_ids[i % len(employee_ids)]

                        if i < 10:
                            # 前10条为已分配状态
                            day = min(28, 5 + (i * 2))  # 确保日期有效
                            allocations.append((
                                material_id, dept_id, emp_id,
                                1 if materials_results[i][2] == 'fixed_asset' else 10 + i,
                                f'2024-0{min(6, (i//3)+1)}-{day:02d}',
                                'allocated',
                                f'分配给{["财务部", "人事部", "技术部", "市场部", "行政部"][i % 5]}使用',
                                admin_id
                            ))
                        else:
                            # 后10条为已消耗状态
                            day = min(28, 10 + ((i-10) * 2))  # 确保日期有效
                            allocations.append((
                                material_id, dept_id, emp_id,
                                1 if materials_results[i][2] == 'fixed_asset' else 5 + i,
                                f'2024-0{min(6, ((i-10)//3)+3)}-{day:02d}',
                                'consumed' if materials_results[i][2] == 'consumable' else 'allocated',
                                f'{"消耗" if materials_results[i][2] == "consumable" else "分配"}给{["财务部", "人事部", "技术部", "市场部", "行政部"][i % 5]}',
                                admin_id
                            ))

                    for allocation in allocations:
                        cursor.execute("""
                            INSERT INTO material_allocations (material_id, department_id, user_id, quantity,
                                                            allocation_date, status, notes, allocated_by)
                            VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                        """, allocation)

        # 插入申请记录数据
        cursor.execute("SELECT COUNT(*) FROM material_requests")
        if cursor.fetchone()[0] == 0:
            # 获取一些物资和用户ID用于创建申请
            cursor.execute("SELECT id, name FROM materials WHERE category = 'consumable' LIMIT 8")
            consumable_materials = cursor.fetchall()

            cursor.execute("SELECT id, real_name, department_id FROM users WHERE role = 'employee'")
            employees = cursor.fetchall()

            if consumable_materials and employees:
                # 创建一些待审核的申请
                requests = [
                    (consumable_materials[0][0], employees[0][0], employees[0][2], 50, '办公室打印需要', 'pending', '2024-06-25'),
                    (consumable_materials[1][0], employees[1][0], employees[1][2], 30, '会议记录用', 'pending', '2024-06-26'),
                    (consumable_materials[2][0], employees[2][0], employees[2][2], 20, '日常办公使用', 'pending', '2024-06-27'),
                    (consumable_materials[3][0], employees[0][0], employees[0][2], 10, '文件整理需要', 'pending', '2024-06-28'),
                    (consumable_materials[4][0], employees[1][0], employees[1][2], 15, '客户资料装订', 'pending', '2024-06-29'),
                    (consumable_materials[5][0], employees[2][0], employees[2][2], 25, '月度报表打印', 'pending', '2024-06-30'),
                ]

                for request in requests:
                    cursor.execute("""
                        INSERT INTO material_requests (material_id, user_id, department_id, quantity, reason, status, request_date)
                        VALUES (%s, %s, %s, %s, %s, %s, %s)
                    """, request)

        connection.commit()
        print("✅ 数据库初始化完成！")
        
        # 显示创建的用户信息
        print("\n创建的测试用户:")
        cursor.execute("SELECT username, real_name, role FROM users")
        for user in cursor.fetchall():
            print(f"  用户名: {user[0]}, 姓名: {user[1]}, 角色: {user[2]}")

        print("\n默认密码:")
        print("  所有用户密码: 123456")
        
    except Exception as e:
        print(f"❌ 数据库初始化失败: {e}")
    finally:
        if 'connection' in locals():
            connection.close()

if __name__ == '__main__':
    init_database()
