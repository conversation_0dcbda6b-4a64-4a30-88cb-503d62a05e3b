#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from dao.database import Database

db = Database()

print("检查物资申请相关表:")
print("=" * 50)

# 检查是否存在material_requests表
print("1. 检查material_requests表是否存在:")
sql = "SHOW TABLES LIKE 'material_requests'"
try:
    results = db.execute_query(sql)
    if results:
        print("   ✅ material_requests表存在")
        
        # 检查表结构
        print("\n2. material_requests表结构:")
        sql = "DESCRIBE material_requests"
        results = db.execute_query(sql)
        for row in results:
            print(f"   {row}")
            
        # 检查表数据
        print("\n3. material_requests表数据:")
        sql = "SELECT * FROM material_requests LIMIT 10"
        results = db.execute_query(sql)
        if results:
            for row in results:
                print(f"   {row}")
        else:
            print("   表中暂无数据")
    else:
        print("   ❌ material_requests表不存在")
        
        # 检查所有表
        print("\n   当前数据库中的所有表:")
        sql = "SHOW TABLES"
        results = db.execute_query(sql)
        for row in results:
            print(f"   - {list(row.values())[0]}")
            
except Exception as e:
    print(f"   错误: {e}")

print("\n" + "=" * 50)

# 测试申请功能
print("4. 测试创建申请:")
try:
    from services.material_service import MaterialService
    from dao.user_dao import UserDAO
    
    material_service = MaterialService()
    user_dao = UserDAO()
    
    # 获取一个普通用户
    user = user_dao.get_user_by_username('zhangsan')
    if user:
        print(f"   用户: {user.real_name} (ID: {user.id})")
        
        # 获取一个物资
        materials = material_service.get_material_list()
        if materials:
            material = materials[0]
            print(f"   物资: {material.name} (ID: {material.id})")
            
            # 尝试创建申请
            try:
                request_id = material_service.request_material(
                    material.id, 1, "测试申请", user
                )
                print(f"   ✅ 申请创建成功，ID: {request_id}")
            except Exception as e:
                print(f"   ❌ 申请创建失败: {e}")
        else:
            print("   ❌ 没有可用的物资")
    else:
        print("   ❌ 用户不存在")
        
except Exception as e:
    print(f"   错误: {e}")
