# 🤖 智能体系统使用教程

## 📋 目录
1. [系统概述](#系统概述)
2. [配置说明](#配置说明)
3. [智能体功能介绍](#智能体功能介绍)
4. [使用指南](#使用指南)
5. [API接口说明](#api接口说明)
6. [常见问题](#常见问题)

## 🌟 系统概述

本智能体系统为金融企业物资管理系统提供AI驱动的智能化功能，包含四个核心智能体：

- **智能申请助手** - 协助用户智能填写申请，预测需求
- **智能审批助手** - 自动分析申请合理性，提供审批建议
- **智能库存管理** - 监控库存水平，预测补货需求
- **智能报表分析** - 自动生成洞察报告，趋势分析

## ⚙️ 配置说明

### 1. DeepSeek API配置

在 `config.py` 文件中配置您的DeepSeek API密钥：

```python
# DeepSeek API配置
DEEPSEEK_API_KEY = 'sk-your-deepseek-api-key-here'  # 请替换为您的API密钥
DEEPSEEK_BASE_URL = 'https://api.deepseek.com'
DEEPSEEK_MODEL = 'deepseek-chat'
```

### 2. 获取DeepSeek API密钥

1. 访问 [DeepSeek官网](https://platform.deepseek.com/)
2. 注册账号并登录
3. 在控制台中创建API密钥
4. 将密钥复制到配置文件中

### 3. 安装依赖

```bash
pip install requests flask pymysql
```

## 🎯 智能体功能介绍

### 1. 智能申请助手 👤

**主要功能：**
- 🎯 **物资推荐** - 基于用户历史和部门需求智能推荐物资
- 📊 **数量预测** - 根据历史数据预测合理申请数量
- ✍️ **理由优化** - 帮助用户完善申请理由
- ✅ **申请验证** - 验证申请的合理性
- 📈 **使用历史分析** - 分析用户的使用模式

**使用场景：**
- 员工不确定需要申请什么物资时
- 不知道申请多少数量合适时
- 需要完善申请理由时

### 2. 智能审批助手 ✅

**主要功能：**
- 🔍 **申请分析** - 自动分析申请的合理性
- 📋 **批量分析** - 处理多个申请的批量分析
- ⚠️ **风险评估** - 多维度风险评分系统
- 💡 **审批建议** - 提供智能审批建议
- 🚨 **欺诈检测** - 检测异常申请模式

**使用场景：**
- 管理员审批申请时需要参考意见
- 批量处理大量申请时
- 识别可疑或异常申请时

### 3. 智能库存管理 ⚙️

**主要功能：**
- 📦 **库存监控** - 实时监控库存水平和状态
- 🔮 **需求预测** - 基于历史数据预测未来需求
- 🛒 **补货建议** - 智能补货提醒和建议
- 🎯 **分配优化** - 优化物资分配策略
- 📊 **使用模式分析** - 分析物资使用模式
- 🔔 **库存警报** - 低库存和异常警报

**使用场景：**
- 定期检查库存状态
- 制定采购计划时
- 优化库存分配策略时

### 4. 智能报表分析 📊

**主要功能：**
- 💡 **洞察报告生成** - 自动生成业务洞察
- 📈 **趋势分析** - 多维度趋势分析
- 🚨 **异常检测** - 智能异常检测和预警
- ⚡ **性能分析** - 系统性能和效率分析
- 💰 **成本分析** - 成本控制和优化建议
- 📊 **效率分析** - 流程效率评估

**使用场景：**
- 生成定期业务报告
- 分析业务趋势和异常
- 优化成本和效率时

## 📖 使用指南

### 1. 访问智能体控制台

1. 登录系统后，访问主控制台：`http://localhost:5000/dashboard`
2. 在页面中找到"🤖 智能体控制台"部分
3. 查看四个智能体的状态和功能

### 2. 使用智能对话功能

在主控制台的"💬 智能对话"部分：

1. **输入问题** - 在输入框中输入您的问题
2. **发送消息** - 点击"发送"按钮或按Enter键
3. **查看回复** - AI助手会智能分析并回复

**示例对话：**
```
用户：我需要申请一些办公用品，有什么推荐吗？
AI助手：根据您的部门和历史记录，我推荐以下物资：
• A4纸 - 办公必需品，库存充足
• 签字笔 - 日常使用频率高
• 文件夹 - 整理文档需要
```

### 3. 使用快速操作

点击快速操作按钮可以执行常用功能：

- **📋 生成综合报告** - 生成包含所有关键指标的综合报告
- **📦 检查库存状态** - 快速查看当前库存状况
- **⚠️ 异常检测** - 检测系统中的异常情况
- **🔧 优化工作流程** - 分析并优化业务流程（仅管理员）

### 4. 查看智能体详情

1. 点击任意智能体卡片
2. 在弹出的模态框中查看详细信息
3. 使用快速操作按钮执行特定功能

## 🔌 API接口说明

### 基础接口

- `GET /agent/health` - 检查系统健康状态
- `GET /agent/status` - 获取智能体状态
- `POST /agent/chat` - 智能对话接口

### 智能申请助手

- `POST /agent/request/assist` - 申请协助
  ```json
  {
    "action": "suggest_materials",
    "user_id": 1,
    "department_id": 1,
    "category": "办公用品"
  }
  ```

### 智能审批助手

- `POST /agent/approval/analyze` - 分析申请
  ```json
  {
    "action": "analyze_request",
    "request_id": 1
  }
  ```

### 智能库存管理

- `GET /agent/inventory/monitor` - 监控库存
- `POST /agent/inventory/predict` - 预测需求

### 智能报表分析

- `GET /agent/report/insights` - 生成洞察
- `GET /agent/report/anomaly_detection` - 异常检测

## ❓ 常见问题

### Q1: 智能体无法正常工作怎么办？

**A:** 请检查以下几点：
1. 确认DeepSeek API密钥配置正确
2. 检查网络连接是否正常
3. 查看系统日志中的错误信息
4. 确认数据库连接正常

### Q2: 如何获得更准确的AI建议？

**A:** 
1. 提供详细的上下文信息
2. 保持历史数据的完整性
3. 定期更新物资信息
4. 使用具体明确的问题描述

### Q3: 智能体的建议是否可靠？

**A:** 
- AI建议基于历史数据和模式分析
- 建议仅供参考，最终决策需要人工确认
- 系统会提供置信度评分
- 重要决策建议结合人工判断

### Q4: 如何优化智能体性能？

**A:**
1. 定期清理和更新数据
2. 收集用户反馈优化模型
3. 监控API调用频率和成本
4. 根据使用情况调整参数

### Q5: 数据安全如何保障？

**A:**
- 所有API调用都经过身份验证
- 敏感数据在传输过程中加密
- 遵循最小权限原则
- 定期备份重要数据

## 🚀 高级功能

### 1. 自定义智能体

您可以根据业务需求扩展智能体功能：

1. 继承 `BaseAgent` 类
2. 实现 `process` 方法
3. 注册到协调中心
4. 添加Web接口

### 2. 批量操作

支持批量处理多个请求：

```python
# 批量分析申请
requests = [request1, request2, request3]
results = approval_agent.batch_analyze(requests)
```

### 3. 定时任务

设置定时任务自动执行智能体功能：

- 每日库存检查
- 周度趋势分析
- 月度综合报告

## 📞 技术支持

如果您在使用过程中遇到问题，请：

1. 查看系统日志文件
2. 检查配置文件设置
3. 参考本教程的常见问题部分
4. 联系技术支持团队

---

**祝您使用愉快！** 🎉
