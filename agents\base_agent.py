#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能体基础类
"""

import json
import logging
from abc import ABC, abstractmethod
from datetime import datetime
from typing import Dict, List, Any, Optional
from dao.database import db

class BaseAgent(ABC):
    """智能体基础类"""
    
    def __init__(self, agent_id: str, name: str, description: str):
        self.agent_id = agent_id
        self.name = name
        self.description = description
        self.created_at = datetime.now()
        self.is_active = True
        self.logger = logging.getLogger(f"Agent.{self.agent_id}")
        
    @abstractmethod
    def process(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """处理输入数据并返回结果"""
        pass

    @abstractmethod
    def get_capabilities(self) -> List[str]:
        """获取智能体能力列表"""
        pass

    def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        try:
            # 基本健康检查
            return AgentResponse(
                True,
                data={
                    'agent_id': self.agent_id,
                    'status': self.status,
                    'last_activity': self.last_activity.isoformat() if self.last_activity else None,
                    'capabilities': self.get_capabilities()
                },
                message="健康检查通过",
                confidence=1.0
            ).to_dict()
        except Exception as e:
            return AgentResponse(False, message=f"健康检查失败: {e}").to_dict()
    
    def log_activity(self, action: str, input_data: Dict, output_data: Dict, user_id: int = None):
        """记录智能体活动"""
        try:
            activity_log = {
                'agent_id': self.agent_id,
                'action': action,
                'input_data': json.dumps(input_data, ensure_ascii=False),
                'output_data': json.dumps(output_data, ensure_ascii=False),
                'user_id': user_id,
                'timestamp': datetime.now()
            }
            
            sql = """
            INSERT INTO agent_activities (agent_id, action, input_data, output_data, user_id, timestamp)
            VALUES (%s, %s, %s, %s, %s, %s)
            """
            
            db.execute_insert(sql, (
                activity_log['agent_id'],
                activity_log['action'],
                activity_log['input_data'],
                activity_log['output_data'],
                activity_log['user_id'],
                activity_log['timestamp']
            ))
            
        except Exception as e:
            self.logger.error(f"记录活动失败: {e}")
    
    def get_status(self) -> Dict[str, Any]:
        """获取智能体状态"""
        return {
            'agent_id': self.agent_id,
            'name': self.name,
            'description': self.description,
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat(),
            'capabilities': self.get_capabilities()
        }
    
    def activate(self):
        """激活智能体"""
        self.is_active = True
        self.logger.info(f"智能体 {self.name} 已激活")
    
    def deactivate(self):
        """停用智能体"""
        self.is_active = False
        self.logger.info(f"智能体 {self.name} 已停用")

class AgentResponse:
    """智能体响应类"""
    
    def __init__(self, success: bool, data: Any = None, message: str = "", 
                 suggestions: List[str] = None, confidence: float = 1.0):
        self.success = success
        self.data = data
        self.message = message
        self.suggestions = suggestions or []
        self.confidence = confidence
        self.timestamp = datetime.now()
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'success': self.success,
            'data': self.data,
            'message': self.message,
            'suggestions': self.suggestions,
            'confidence': self.confidence,
            'timestamp': self.timestamp.isoformat()
        }

class AgentError(Exception):
    """智能体异常类"""
    
    def __init__(self, message: str, agent_id: str = None, error_code: str = None):
        super().__init__(message)
        self.agent_id = agent_id
        self.error_code = error_code
        self.timestamp = datetime.now()
