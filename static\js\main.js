// 主要JavaScript功能

// 页面加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    // 初始化所有功能
    initializeAlerts();
    initializeForms();
    initializeCharts();
    initializeModals();
});

// 初始化消息提示
function initializeAlerts() {
    // 自动隐藏消息提示
    const alerts = document.querySelectorAll('.alert');
    alerts.forEach(alert => {
        setTimeout(() => {
            alert.style.opacity = '0';
            setTimeout(() => {
                alert.remove();
            }, 300);
        }, 5000);
    });
}

// 初始化表单
function initializeForms() {
    // 表单验证
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            if (!validateForm(this)) {
                e.preventDefault();
            }
        });
    });
    
    // 数字输入框验证
    const numberInputs = document.querySelectorAll('input[type="number"]');
    numberInputs.forEach(input => {
        input.addEventListener('input', function() {
            if (this.value < 0) {
                this.value = 0;
            }
        });
    });
}

// 表单验证
function validateForm(form) {
    let isValid = true;
    const requiredFields = form.querySelectorAll('[required]');
    
    requiredFields.forEach(field => {
        if (!field.value.trim()) {
            showFieldError(field, '此字段为必填项');
            isValid = false;
        } else {
            clearFieldError(field);
        }
    });
    
    return isValid;
}

// 显示字段错误
function showFieldError(field, message) {
    clearFieldError(field);
    
    const errorDiv = document.createElement('div');
    errorDiv.className = 'field-error';
    errorDiv.style.color = '#FFA07A';
    errorDiv.style.fontSize = '0.875rem';
    errorDiv.style.marginTop = '0.25rem';
    errorDiv.textContent = message;
    
    field.parentNode.appendChild(errorDiv);
    field.style.borderColor = '#FFA07A';
}

// 清除字段错误
function clearFieldError(field) {
    const errorDiv = field.parentNode.querySelector('.field-error');
    if (errorDiv) {
        errorDiv.remove();
    }
    field.style.borderColor = '';
}

// 初始化图表
function initializeCharts() {
    // 如果页面有统计图表容器，则初始化图表
    const chartContainer = document.getElementById('statisticsChart');
    if (chartContainer) {
        loadStatisticsChart();
    }
}

// 加载统计图表
function loadStatisticsChart() {
    fetch('/api/statistics')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                renderChart(data.data);
            }
        })
        .catch(error => {
            console.error('加载统计数据失败:', error);
        });
}

// 渲染图表（简单的柱状图）
function renderChart(data) {
    const chartContainer = document.getElementById('statisticsChart');
    if (!chartContainer || !data.category_stats) return;
    
    const categories = Object.keys(data.category_stats);
    const values = categories.map(cat => data.category_stats[cat].count);
    
    // 创建简单的CSS柱状图
    chartContainer.innerHTML = '';
    
    categories.forEach((category, index) => {
        const bar = document.createElement('div');
        bar.className = 'chart-bar';
        bar.style.cssText = `
            display: flex;
            align-items: center;
            margin-bottom: 1rem;
        `;
        
        const label = document.createElement('div');
        label.style.cssText = `
            width: 100px;
            text-align: right;
            margin-right: 1rem;
            font-size: 0.875rem;
        `;
        label.textContent = category === 'fixed_asset' ? '固定资产' : '耗材';
        
        const barContainer = document.createElement('div');
        barContainer.style.cssText = `
            flex: 1;
            height: 30px;
            background-color: #f0f0f0;
            border-radius: 15px;
            overflow: hidden;
            position: relative;
        `;
        
        const barFill = document.createElement('div');
        const maxValue = Math.max(...values);
        const percentage = maxValue > 0 ? (values[index] / maxValue) * 100 : 0;
        barFill.style.cssText = `
            height: 100%;
            width: ${percentage}%;
            background: linear-gradient(135deg, ${index === 0 ? '#FFB6C1' : '#98FB98'}, ${index === 0 ? '#E6E6FA' : '#90EE90'});
            border-radius: 15px;
            transition: width 0.5s ease;
        `;
        
        const valueLabel = document.createElement('div');
        valueLabel.style.cssText = `
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 0.875rem;
            font-weight: bold;
        `;
        valueLabel.textContent = values[index];
        
        barContainer.appendChild(barFill);
        barContainer.appendChild(valueLabel);
        bar.appendChild(label);
        bar.appendChild(barContainer);
        chartContainer.appendChild(bar);
    });
}

// 初始化模态框
function initializeModals() {
    // 确认删除模态框
    const deleteButtons = document.querySelectorAll('[data-confirm-delete]');
    deleteButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            const message = this.getAttribute('data-confirm-delete') || '确定要删除吗？';
            if (confirm(message)) {
                window.location.href = this.href;
            }
        });
    });
}

// 工具函数
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('zh-CN');
}

function formatCurrency(amount) {
    return new Intl.NumberFormat('zh-CN', {
        style: 'currency',
        currency: 'CNY'
    }).format(amount);
}

// 搜索功能
function initializeSearch() {
    const searchInput = document.getElementById('searchInput');
    if (searchInput) {
        searchInput.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            const tableRows = document.querySelectorAll('tbody tr');
            
            tableRows.forEach(row => {
                const text = row.textContent.toLowerCase();
                if (text.includes(searchTerm)) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        });
    }
}

// 导出功能
function exportTable(tableId, filename) {
    const table = document.getElementById(tableId);
    if (!table) return;
    
    let csv = '';
    const rows = table.querySelectorAll('tr');
    
    rows.forEach(row => {
        const cols = row.querySelectorAll('td, th');
        const rowData = Array.from(cols).map(col => {
            return '"' + col.textContent.replace(/"/g, '""') + '"';
        }).join(',');
        csv += rowData + '\n';
    });
    
    const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', filename + '.csv');
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// 页面加载动画
function showLoading() {
    const loading = document.createElement('div');
    loading.id = 'loading';
    loading.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(255, 255, 255, 0.8);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 9999;
    `;
    loading.innerHTML = '<div style="color: #FFB6C1; font-size: 1.5rem;">加载中...</div>';
    document.body.appendChild(loading);
}

function hideLoading() {
    const loading = document.getElementById('loading');
    if (loading) {
        loading.remove();
    }
}
