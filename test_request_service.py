#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from services.material_service import MaterialService
from models.user import User

# 创建测试用户
admin_user = User(
    id=21,
    username='admin',
    real_name='系统管理员',
    role='admin',
    department_id=16
)

material_service = MaterialService()

try:
    print("测试获取待审核申请:")
    pending_requests = material_service.get_pending_requests(admin_user)
    print(f"待审核申请数量: {len(pending_requests)}")
    
    if pending_requests:
        print("\n前3个待审核申请:")
        for i, request in enumerate(pending_requests[:3]):
            print(f"  {i+1}. ID: {request['id']}")
            print(f"     物资: {request['material_name']}")
            print(f"     申请人: {request['user_name']}")
            print(f"     数量: {request['quantity']}")
            print(f"     原因: {request['reason']}")
            print(f"     状态: {request['status']}")
            print()
    
except Exception as e:
    print(f"错误: {e}")
    import traceback
    traceback.print_exc()
