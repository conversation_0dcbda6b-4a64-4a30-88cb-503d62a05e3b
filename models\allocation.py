from datetime import datetime, date

class MaterialAllocation:
    def __init__(self, id=None, material_id=None, department_id=None, user_id=None,
                 allocated_by=None, quantity=1, allocation_date=None, return_date=None,
                 status='allocated', notes=None, created_at=None, updated_at=None):
        self.id = id
        self.material_id = material_id
        self.department_id = department_id
        self.user_id = user_id
        self.allocated_by = allocated_by
        self.quantity = quantity
        self.allocation_date = allocation_date or date.today()
        self.return_date = return_date
        self.status = status  # 'allocated', 'returned', 'consumed'
        self.notes = notes
        self.created_at = created_at or datetime.now()
        self.updated_at = updated_at or datetime.now()
    
    def is_active(self):
        """检查分配是否仍然有效"""
        return self.status == 'allocated'
    
    def is_returned(self):
        """检查是否已归还"""
        return self.status == 'returned'
    
    def is_consumed(self):
        """检查是否已消耗"""
        return self.status == 'consumed'
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'material_id': self.material_id,
            'department_id': self.department_id,
            'user_id': self.user_id,
            'allocated_by': self.allocated_by,
            'quantity': self.quantity,
            'allocation_date': self.allocation_date.isoformat() if self.allocation_date else None,
            'return_date': self.return_date.isoformat() if self.return_date else None,
            'status': self.status,
            'notes': self.notes,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
    
    @classmethod
    def from_dict(cls, data):
        """从字典创建MaterialAllocation对象"""
        return cls(
            id=data.get('id'),
            material_id=data.get('material_id'),
            department_id=data.get('department_id'),
            user_id=data.get('user_id'),
            allocated_by=data.get('allocated_by'),
            quantity=data.get('quantity', 1),
            allocation_date=data.get('allocation_date'),
            return_date=data.get('return_date'),
            status=data.get('status', 'allocated'),
            notes=data.get('notes'),
            created_at=data.get('created_at'),
            updated_at=data.get('updated_at')
        )


class MaterialRequest:
    def __init__(self, id=None, material_id=None, user_id=None, department_id=None,
                 quantity=1, request_date=None, status='pending', approved_by=None,
                 approval_date=None, reason=None, notes=None, created_at=None, updated_at=None):
        self.id = id
        self.material_id = material_id
        self.user_id = user_id
        self.department_id = department_id
        self.quantity = quantity
        self.request_date = request_date or date.today()
        self.status = status  # 'pending', 'approved', 'rejected'
        self.approved_by = approved_by
        self.approval_date = approval_date
        self.reason = reason
        self.notes = notes
        self.created_at = created_at or datetime.now()
        self.updated_at = updated_at or datetime.now()
    
    def is_pending(self):
        """检查是否待审核"""
        return self.status == 'pending'
    
    def is_approved(self):
        """检查是否已批准"""
        return self.status == 'approved'
    
    def is_rejected(self):
        """检查是否已拒绝"""
        return self.status == 'rejected'
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'material_id': self.material_id,
            'user_id': self.user_id,
            'department_id': self.department_id,
            'quantity': self.quantity,
            'request_date': self.request_date.isoformat() if self.request_date else None,
            'status': self.status,
            'approved_by': self.approved_by,
            'approval_date': self.approval_date.isoformat() if self.approval_date else None,
            'reason': self.reason,
            'notes': self.notes,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
    
    @classmethod
    def from_dict(cls, data):
        """从字典创建MaterialRequest对象"""
        return cls(
            id=data.get('id'),
            material_id=data.get('material_id'),
            user_id=data.get('user_id'),
            department_id=data.get('department_id'),
            quantity=data.get('quantity', 1),
            request_date=data.get('request_date'),
            status=data.get('status', 'pending'),
            approved_by=data.get('approved_by'),
            approval_date=data.get('approval_date'),
            reason=data.get('reason'),
            notes=data.get('notes'),
            created_at=data.get('created_at'),
            updated_at=data.get('updated_at')
        )
