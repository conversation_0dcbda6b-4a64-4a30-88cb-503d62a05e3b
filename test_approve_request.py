#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from services.material_service import MaterialService
from models.user import User

# 创建管理员用户
admin_user = User(
    id=1,
    username='admin',
    real_name='系统管理员',
    role='admin',
    department_id=1
)

material_service = MaterialService()

try:
    print("测试管理员批准申请:")
    
    # 获取待审核申请
    pending_requests = material_service.get_pending_requests(admin_user)
    print(f"待审核申请数量: {len(pending_requests)}")
    
    if pending_requests:
        # 选择第一个申请进行批准
        request = pending_requests[0]
        print(f"\n选择申请:")
        print(f"申请ID: {request['id']}")
        print(f"物资: {request['material_name']}")
        print(f"申请人: {request['user_name']}")
        print(f"数量: {request['quantity']}")
        print(f"理由: {request['reason']}")
        
        # 批准申请
        result = material_service.approve_request(
            request['id'],
            admin_user,
            "管理员批准"
        )
        
        print(f"批准成功! 分配记录ID: {result.id}")
        print(f"分配状态: {result.status}")
        
    else:
        print("没有待审核的申请")
    
except Exception as e:
    print(f"错误: {e}")
    import traceback
    traceback.print_exc()
