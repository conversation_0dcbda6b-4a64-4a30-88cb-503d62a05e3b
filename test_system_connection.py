#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试系统连接和基本功能
"""

import pymysql
from config import Config

def test_database_connection():
    """测试数据库连接"""
    print("测试数据库连接...")
    try:
        conn = pymysql.connect(**Config.DB_CONFIG)
        cursor = conn.cursor()
        
        # 测试基本查询
        cursor.execute("SELECT COUNT(*) FROM users")
        user_count = cursor.fetchone()[0]
        print(f"✅ 数据库连接成功！用户总数: {user_count}")
        
        cursor.execute("SELECT COUNT(*) FROM materials")
        material_count = cursor.fetchone()[0]
        print(f"✅ 物资总数: {material_count}")
        
        cursor.execute("SELECT COUNT(*) FROM departments")
        dept_count = cursor.fetchone()[0]
        print(f"✅ 科室总数: {dept_count}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return False

def test_flask_app():
    """测试Flask应用"""
    print("\n测试Flask应用...")
    try:
        import requests
        response = requests.get('http://localhost:5000')
        if response.status_code == 200:
            print("✅ Flask应用运行正常！")
            return True
        else:
            print(f"❌ Flask应用响应异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Flask应用测试失败: {e}")
        return False

if __name__ == '__main__':
    print("=" * 50)
    print("金融企业物资管理系统 - 连接测试")
    print("=" * 50)
    
    # 测试数据库连接
    db_ok = test_database_connection()
    
    # 测试Flask应用
    app_ok = test_flask_app()
    
    print("\n" + "=" * 50)
    if db_ok and app_ok:
        print("🎉 系统测试通过！系统已准备就绪。")
        print("\n登录信息:")
        print("- 管理员账号: admin / 123456")
        print("- 普通用户: zhangsan / 123456")
        print("- 访问地址: http://localhost:5000")
    else:
        print("❌ 系统测试失败，请检查配置。")
    print("=" * 50)
