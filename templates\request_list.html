{% extends "base.html" %}

{% block title %}申请审核{% endblock %}

{% block extra_css %}
<style>
/* 优化表格样式 */
.table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 0;
    background: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.table th {
    background: var(--primary-color);
    color: white;
    padding: 12px 8px;
    text-align: center;
    font-weight: 600;
    font-size: 0.9rem;
    border: none;
}

.table td {
    padding: 12px 8px;
    text-align: center;
    vertical-align: middle;
    border-bottom: 1px solid var(--border-color);
    font-size: 0.9rem;
}

.table tbody tr:hover {
    background-color: var(--light-color);
}

.table tbody tr:last-child td {
    border-bottom: none;
}

/* 操作表单样式 */
.action-forms {
    display: flex;
    flex-direction: column;
    gap: 8px;
    align-items: center;
}

.action-form {
    display: flex;
    align-items: center;
    gap: 5px;
}

.action-form input {
    width: 100px;
    font-size: 0.8rem;
}

.action-form button {
    font-size: 0.8rem;
    padding: 4px 8px;
}

/* 状态徽章样式 */
.badge {
    display: inline-block;
    padding: 0.4rem 0.8rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 500;
    text-align: center;
    white-space: nowrap;
}

.badge-warning {
    background-color: var(--warning-color);
    color: var(--text-color);
}

.badge-success {
    background-color: var(--success-color);
    color: var(--text-color);
}

.badge-danger {
    background-color: var(--danger-color);
    color: white;
}
</style>
{% endblock %}

{% block content %}
<div class="container">
    <div class="card">
        <div class="card-header">
            <h2 class="card-title">申请审核</h2>
            <p class="text-muted">管理员可以在此审核员工的物资申请</p>
        </div>
        
        {% if requests %}
        <div class="table-responsive">
            <table class="table">
                <thead>
                    <tr>
                        <th>申请编号</th>
                        <th>申请人</th>
                        <th>科室</th>
                        <th>物资名称</th>
                        <th>申请数量</th>
                        <th>申请理由</th>
                        <th>申请时间</th>
                        <th>状态</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    {% for request in requests %}
                    <tr>
                        <td>{{ request.id }}</td>
                        <td>{{ request.user_name }}</td>
                        <td>{{ request.department_name }}</td>
                        <td>{{ request.material_name }}</td>
                        <td>{{ request.quantity }}</td>
                        <td>{{ request.reason }}</td>
                        <td>{{ request.created_at.strftime('%Y-%m-%d %H:%M') if request.created_at else '' }}</td>
                        <td>
                            {% if request.status == 'pending' %}
                                <span class="badge badge-warning">待审核</span>
                            {% elif request.status == 'approved' %}
                                <span class="badge badge-success">已批准</span>
                            {% elif request.status == 'rejected' %}
                                <span class="badge badge-danger">已拒绝</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if request.status == 'pending' %}
                            <div class="action-forms">
                                <form method="POST" action="{{ url_for('material.approve_request', request_id=request.id) }}" class="action-form">
                                    <input type="text" name="notes" placeholder="审核备注" class="form-control form-control-sm">
                                    <button type="submit" class="btn btn-success btn-sm">批准</button>
                                </form>
                                <form method="POST" action="{{ url_for('material.reject_request', request_id=request.id) }}" class="action-form">
                                    <input type="text" name="notes" placeholder="拒绝理由" class="form-control form-control-sm">
                                    <button type="submit" class="btn btn-danger btn-sm">拒绝</button>
                                </form>
                            </div>
                            {% else %}
                                <span class="text-muted">已处理</span>
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
            <h4 class="text-muted">暂无待审核申请</h4>
            <p class="text-muted">当前没有需要审核的物资申请</p>
        </div>
        {% endif %}
    </div>
</div>


{% endblock %}
