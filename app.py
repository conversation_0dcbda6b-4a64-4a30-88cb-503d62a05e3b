from flask import Flask, redirect, url_for, session
from config import Config

def create_app():
    app = Flask(__name__)
    app.config.from_object(Config)

    # 注册蓝图
    from controllers.auth_controller import auth_bp
    from controllers.material_controller import material_bp
    from controllers.report_controller import report_bp

    app.register_blueprint(auth_bp, url_prefix='/auth')
    app.register_blueprint(material_bp)
    app.register_blueprint(report_bp)

    # 根路由
    @app.route('/')
    def index():
        if 'user_id' in session:
            return redirect(url_for('material.dashboard'))
        return redirect(url_for('auth.login'))

    return app

if __name__ == '__main__':
    app = create_app()
    app.run(debug=True, host='0.0.0.0', port=5000)
