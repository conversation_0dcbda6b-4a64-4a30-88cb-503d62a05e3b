{% extends "base.html" %}

{% block title %}API配置{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">🔑 API密钥配置</h3>
                    <p class="text-muted mb-0">配置DeepSeek API密钥以启用智能体功能</p>
                </div>
                
                <div class="card-body">
                    <!-- 当前状态 -->
                    <div class="alert alert-info">
                        <h6>📋 当前状态</h6>
                        <div id="api-status">
                            <span class="spinner-border spinner-border-sm me-2"></span>
                            正在检查API配置状态...
                        </div>
                    </div>
                    
                    <!-- 配置表单 -->
                    <form id="api-config-form">
                        <div class="mb-3">
                            <label for="api-key" class="form-label">
                                <strong>DeepSeek API密钥</strong>
                                <span class="text-danger">*</span>
                            </label>
                            <div class="input-group">
                                <input type="password" class="form-control" id="api-key" name="api_key" 
                                       placeholder="sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx" required>
                                <button class="btn btn-outline-secondary" type="button" id="toggle-password">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                            <div class="form-text">
                                请输入您的DeepSeek API密钥。如果没有密钥，请访问 
                                <a href="https://platform.deepseek.com/" target="_blank">DeepSeek官网</a> 获取。
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="base-url" class="form-label">
                                <strong>API基础URL</strong>
                            </label>
                            <input type="url" class="form-control" id="base-url" name="base_url" 
                                   value="https://api.deepseek.com" readonly>
                            <div class="form-text">默认API端点，通常不需要修改</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="model" class="form-label">
                                <strong>模型名称</strong>
                            </label>
                            <select class="form-select" id="model" name="model">
                                <option value="deepseek-chat">deepseek-chat (推荐)</option>
                                <option value="deepseek-coder">deepseek-coder</option>
                            </select>
                            <div class="form-text">选择要使用的AI模型</div>
                        </div>
                        
                        <div class="d-flex gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>保存配置
                            </button>
                            <button type="button" class="btn btn-outline-info" id="test-api">
                                <i class="fas fa-vial me-2"></i>测试连接
                            </button>
                            <a href="{{ url_for('material.dashboard') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left me-2"></i>返回控制台
                            </a>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- 使用说明 -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="card-title">📖 使用说明</h5>
                </div>
                <div class="card-body">
                    <h6>1. 获取API密钥</h6>
                    <ol>
                        <li>访问 <a href="https://platform.deepseek.com/" target="_blank">DeepSeek官网</a></li>
                        <li>注册账号并登录</li>
                        <li>在控制台中创建API密钥</li>
                        <li>复制密钥到上方输入框</li>
                    </ol>
                    
                    <h6 class="mt-3">2. 配置完成后</h6>
                    <ul>
                        <li>✅ 智能体功能将自动启用</li>
                        <li>✅ 智能对话可以正常使用</li>
                        <li>✅ 快速操作按钮将正常工作</li>
                        <li>✅ 所有AI功能都将可用</li>
                    </ul>
                    
                    <h6 class="mt-3">3. 安全提示</h6>
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>重要：</strong>请妥善保管您的API密钥，不要与他人分享。
                        密钥将安全存储在服务器端，仅用于AI功能调用。
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    checkApiStatus();
    
    // 切换密码显示
    document.getElementById('toggle-password').addEventListener('click', function() {
        const input = document.getElementById('api-key');
        const icon = this.querySelector('i');
        
        if (input.type === 'password') {
            input.type = 'text';
            icon.className = 'fas fa-eye-slash';
        } else {
            input.type = 'password';
            icon.className = 'fas fa-eye';
        }
    });
    
    // 表单提交
    document.getElementById('api-config-form').addEventListener('submit', function(e) {
        e.preventDefault();
        saveApiConfig();
    });
    
    // 测试API连接
    document.getElementById('test-api').addEventListener('click', function() {
        testApiConnection();
    });
});

function checkApiStatus() {
    fetch('/agent/api_status')
        .then(response => response.json())
        .then(data => {
            const statusDiv = document.getElementById('api-status');
            
            if (data.success && data.data.configured) {
                statusDiv.innerHTML = `
                    <i class="fas fa-check-circle text-success me-2"></i>
                    API已配置并可用 - 模型: ${data.data.model}
                `;
                statusDiv.parentElement.className = 'alert alert-success';
            } else {
                statusDiv.innerHTML = `
                    <i class="fas fa-exclamation-circle text-warning me-2"></i>
                    API未配置或不可用，请配置API密钥
                `;
                statusDiv.parentElement.className = 'alert alert-warning';
            }
        })
        .catch(error => {
            console.error('检查API状态失败:', error);
            document.getElementById('api-status').innerHTML = `
                <i class="fas fa-times-circle text-danger me-2"></i>
                无法检查API状态
            `;
        });
}

function saveApiConfig() {
    const formData = new FormData(document.getElementById('api-config-form'));
    const data = Object.fromEntries(formData);
    
    if (!data.api_key) {
        alert('请输入API密钥');
        return;
    }
    
    // 显示保存状态
    const submitBtn = document.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>保存中...';
    submitBtn.disabled = true;
    
    fetch('/agent/save_api_config', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('API配置保存成功！');
            checkApiStatus();
            // 清空密钥输入框
            document.getElementById('api-key').value = '';
        } else {
            alert('保存失败：' + (data.error || '未知错误'));
        }
    })
    .catch(error => {
        console.error('保存API配置失败:', error);
        alert('保存失败，请检查网络连接');
    })
    .finally(() => {
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    });
}

function testApiConnection() {
    const apiKey = document.getElementById('api-key').value;
    
    if (!apiKey) {
        alert('请先输入API密钥');
        return;
    }
    
    const testBtn = document.getElementById('test-api');
    const originalText = testBtn.innerHTML;
    testBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>测试中...';
    testBtn.disabled = true;
    
    fetch('/agent/test_api', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            api_key: apiKey,
            base_url: document.getElementById('base-url').value,
            model: document.getElementById('model').value
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('API连接测试成功！\n响应：' + data.data.response);
        } else {
            alert('API连接测试失败：' + (data.error || '未知错误'));
        }
    })
    .catch(error => {
        console.error('API测试失败:', error);
        alert('测试失败，请检查网络连接');
    })
    .finally(() => {
        testBtn.innerHTML = originalText;
        testBtn.disabled = false;
    });
}
</script>
{% endblock %}
