{% extends "base.html" %}

{% block title %}科室报表{% endblock %}

{% block content %}
<div class="container">
    <div class="card">
        <div class="card-header">
            <h2 class="card-title">科室报表</h2>
            <p class="text-muted">查看各科室物资分配情况</p>
        </div>
        
        <!-- 科室选择 -->
        <div class="filter-section">
            <form method="GET" class="filter-form">
                <div class="form-group">
                    <label for="department_id">选择科室：</label>
                    <select name="department_id" id="department_id" class="form-control" onchange="this.form.submit()">
                        <option value="">请选择科室</option>
                        {% for dept in departments %}
                        <option value="{{ dept.id }}" {% if selected_department_id == dept.id %}selected{% endif %}>
                            {{ dept.name }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
            </form>
        </div>
        
        {% if report_data %}
        <!-- 科室概览 -->
        <div class="department-overview">
            <div class="overview-grid">
                <div class="overview-card">
                    <div class="overview-icon">
                        <i class="fas fa-building"></i>
                    </div>
                    <div class="overview-content">
                        <h3>{{ report_data.department_name }}</h3>
                        <p>科室名称</p>
                    </div>
                </div>
                <div class="overview-card">
                    <div class="overview-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="overview-content">
                        <h3>{{ report_data.total_users }}</h3>
                        <p>科室人数</p>
                    </div>
                </div>
                <div class="overview-card">
                    <div class="overview-icon">
                        <i class="fas fa-boxes"></i>
                    </div>
                    <div class="overview-content">
                        <h3>{{ report_data.total_materials }}</h3>
                        <p>分配物资数</p>
                    </div>
                </div>
                <div class="overview-card">
                    <div class="overview-icon">
                        <i class="fas fa-dollar-sign"></i>
                    </div>
                    <div class="overview-content">
                        <h3>¥{{ "%.2f"|format(report_data.total_value) }}</h3>
                        <p>物资总价值</p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 物资分配详情 -->
        {% if report_data.allocations %}
        <div class="allocations-section">
            <h3>物资分配详情</h3>
            <div class="table-responsive">
                <table class="table">
                    <thead>
                        <tr>
                            <th>物资名称</th>
                            <th>分类</th>
                            <th>分配用户</th>
                            <th>分配数量</th>
                            <th>单价</th>
                            <th>总价值</th>
                            <th>分配时间</th>
                            <th>状态</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for allocation in report_data.allocations %}
                        <tr>
                            <td>{{ allocation.material_name }}</td>
                            <td>
                                {% if allocation.category == 'fixed_asset' %}
                                    <span class="badge badge-info">固定资产</span>
                                {% else %}
                                    <span class="badge badge-warning">消耗品</span>
                                {% endif %}
                            </td>
                            <td>{{ allocation.user_name or '科室公用' }}</td>
                            <td>{{ allocation.quantity }}</td>
                            <td>¥{{ "%.2f"|format(allocation.unit_price) }}</td>
                            <td>¥{{ "%.2f"|format(allocation.total_value) }}</td>
                            <td>{{ allocation.allocated_at.strftime('%Y-%m-%d') if allocation.allocated_at else '' }}</td>
                            <td>
                                {% if allocation.status == 'active' %}
                                    <span class="badge badge-success">正常</span>
                                {% elif allocation.status == 'returned' %}
                                    <span class="badge badge-secondary">已归还</span>
                                {% elif allocation.status == 'scrapped' %}
                                    <span class="badge badge-danger">已报废</span>
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
        {% endif %}
        
        <!-- 导出功能 -->
        <div class="export-section">
            <h3>导出报表</h3>
            <div class="export-buttons">
                <a href="{{ url_for('report.export_allocation_report', department_id=selected_department_id) }}" class="btn btn-success">
                    <i class="fas fa-download"></i> 导出科室分配报表
                </a>
            </div>
        </div>
        
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-chart-bar fa-3x text-muted mb-3"></i>
            <h4 class="text-muted">请选择科室</h4>
            <p class="text-muted">选择科室后查看详细的分配报表</p>
        </div>
        {% endif %}
    </div>
</div>

<style>
.filter-section {
    padding: 1rem;
    background-color: var(--light-color);
    border-bottom: 1px solid var(--border-color);
}

.filter-form .form-group {
    margin-bottom: 0;
    max-width: 300px;
}

.department-overview {
    padding: 1.5rem;
    background-color: var(--light-color);
    border-bottom: 1px solid var(--border-color);
}

.overview-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.overview-card {
    background: white;
    padding: 1.5rem;
    border-radius: 15px;
    display: flex;
    align-items: center;
    gap: 1rem;
    box-shadow: 0 2px 8px var(--shadow-color);
    border: 1px solid var(--border-color);
}

.overview-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background-color: var(--accent-color);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-color);
    font-size: 1.5rem;
}

.overview-content h3 {
    margin: 0;
    font-size: 1.8rem;
    font-weight: bold;
    color: var(--text-color);
}

.overview-content p {
    margin: 0;
    color: var(--text-muted);
    font-size: 0.9rem;
}

.allocations-section {
    padding: 1.5rem;
}

.allocations-section h3 {
    margin-bottom: 1rem;
    color: var(--text-color);
}

.badge {
    padding: 0.4rem 0.8rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 500;
}

.badge-info {
    background-color: var(--info-color);
    color: var(--text-color);
}

.badge-warning {
    background-color: var(--warning-color);
    color: var(--text-color);
}

.badge-success {
    background-color: var(--success-color);
    color: var(--text-color);
}

.badge-secondary {
    background-color: var(--secondary-color);
    color: var(--text-color);
}

.badge-danger {
    background-color: var(--danger-color);
    color: var(--text-color);
}

.export-section {
    padding: 1.5rem;
    border-top: 1px solid var(--border-color);
}

.export-section h3 {
    margin-bottom: 1rem;
    color: var(--text-color);
}

.export-buttons .btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}
</style>
{% endblock %}
