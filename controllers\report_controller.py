from flask import Blueprint, request, render_template, redirect, url_for, session, flash, jsonify, send_file
from controllers.auth_controller import login_required, admin_required, get_current_user
from services.report_service import ReportService
from services.auth_service import AuthService
from models.user import User
from datetime import datetime

report_bp = Blueprint('report', __name__)
report_service = ReportService()
auth_service = AuthService()

@report_bp.route('/reports')
@login_required
def report_dashboard():
    """报表仪表板"""
    user_info = get_current_user()
    user = User.from_dict(user_info)
    
    try:
        # 获取统计信息
        statistics = report_service.get_material_statistics(user)
        category_stats = report_service.get_category_statistics(user)
        
        return render_template('report_dashboard.html', 
                             user=user_info,
                             statistics=statistics,
                             category_stats=category_stats)
    except Exception as e:
        flash(f'加载报表失败：{str(e)}', 'error')
        return render_template('report_dashboard.html', user=user_info)

@report_bp.route('/reports/materials')
@login_required
def material_report():
    """物资报表"""
    user_info = get_current_user()
    user = User.from_dict(user_info)
    
    category = request.args.get('category')
    
    try:
        statistics = report_service.get_material_statistics(user)
        category_stats = report_service.get_category_statistics(user)
        
        return render_template('material_report.html',
                             user=user_info,
                             statistics=statistics,
                             category_stats=category_stats,
                             selected_category=category)
    except Exception as e:
        flash(f'生成物资报表失败：{str(e)}', 'error')
        return render_template('material_report.html', user=user_info)

@report_bp.route('/reports/departments')
@login_required
def department_report():
    """科室报表"""
    user_info = get_current_user()
    user = User.from_dict(user_info)
    
    department_id = request.args.get('department_id', type=int)
    
    try:
        # 获取可访问的科室
        departments = auth_service.get_accessible_departments(user)
        
        report_data = None
        if department_id:
            report_data = report_service.get_department_material_report(department_id, user)
        
        return render_template('department_report.html',
                             user=user_info,
                             departments=departments,
                             report_data=report_data,
                             selected_department_id=department_id)
    except Exception as e:
        flash(f'生成科室报表失败：{str(e)}', 'error')
        return render_template('department_report.html', user=user_info)

@report_bp.route('/reports/export/materials')
@login_required
def export_material_inventory():
    """导出物资台账Excel"""
    user_info = get_current_user()
    user = User.from_dict(user_info)
    
    category = request.args.get('category')
    
    try:
        excel_file = report_service.generate_material_inventory_excel(user, category)
        
        filename = f"物资台账_{category if category else '全部'}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        
        return send_file(
            excel_file,
            as_attachment=True,
            download_name=filename,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
    except Exception as e:
        flash(f'导出失败：{str(e)}', 'error')
        return redirect(url_for('report.material_report'))

@report_bp.route('/reports/export/allocations')
@login_required
def export_allocation_report():
    """导出分配报表Excel"""
    user_info = get_current_user()
    user = User.from_dict(user_info)
    
    department_id = request.args.get('department_id', type=int)
    
    try:
        excel_file = report_service.generate_allocation_report_excel(user, department_id)
        
        if department_id:
            filename = f"科室分配报表_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        else:
            filename = f"分配报表_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        
        return send_file(
            excel_file,
            as_attachment=True,
            download_name=filename,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
    except Exception as e:
        flash(f'导出失败：{str(e)}', 'error')
        return redirect(url_for('report.department_report'))

@report_bp.route('/api/statistics')
@login_required
def api_statistics():
    """API：获取统计数据"""
    user_info = get_current_user()
    user = User.from_dict(user_info)

    try:
        statistics = report_service.get_material_statistics(user)
        category_stats = report_service.get_category_statistics(user)

        return jsonify({
            'success': True,
            'data': {
                'statistics': statistics,
                'category_stats': category_stats
            }
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': str(e)
        }), 500

@report_bp.route('/reports/advanced')
@login_required
def advanced_statistics():
    """高级统计查询"""
    user_info = get_current_user()
    user = User.from_dict(user_info)

    # 获取查询参数
    category = request.args.get('category')
    department_id = request.args.get('department_id', type=int)
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')

    try:
        # 获取科室列表
        departments = auth_service.get_accessible_departments(user)

        # 获取统计数据
        stats_data = None
        if any([category, department_id, start_date, end_date]):
            stats_data = report_service.get_advanced_statistics(
                user, category, department_id, start_date, end_date
            )

        return render_template('advanced_statistics.html',
                             user=user_info,
                             departments=departments,
                             stats_data=stats_data,
                             filters={
                                 'category': category,
                                 'department_id': department_id,
                                 'start_date': start_date,
                                 'end_date': end_date
                             })
    except Exception as e:
        flash(f'获取统计数据失败：{str(e)}', 'error')
        return render_template('advanced_statistics.html',
                             user=user_info,
                             departments=[],
                             stats_data=None,
                             filters={})
