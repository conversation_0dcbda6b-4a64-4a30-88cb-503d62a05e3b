#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from services.material_service import MaterialService
from dao.user_dao import UserDAO
from models.user import User

material_service = MaterialService()
user_dao = UserDAO()

print("测试管理员dashboard功能:")
print("=" * 50)

# 获取管理员用户
admin_user = user_dao.get_user_by_username('admin')
if admin_user:
    print(f"管理员用户: {admin_user.real_name} (角色: {admin_user.role})")
    print(f"是否为管理员: {admin_user.is_admin()}")
    
    try:
        # 测试获取待审核申请
        print("\n1. 获取待审核申请:")
        pending_requests = material_service.get_pending_requests(admin_user)
        print(f"   待审核申请数量: {len(pending_requests)}")
        
        for i, request in enumerate(pending_requests[:5]):  # 只显示前5个
            print(f"   申请 {i+1}:")
            print(f"     ID: {request.get('id')}")
            print(f"     物资: {request.get('material_name')}")
            print(f"     申请人: {request.get('user_name')}")
            print(f"     科室: {request.get('department_name')}")
            print(f"     数量: {request.get('quantity')}")
            print(f"     状态: {request.get('status')}")
            print(f"     申请日期: {request.get('request_date')}")
            print()
            
    except Exception as e:
        print(f"   错误: {e}")
        import traceback
        traceback.print_exc()
        
    try:
        # 测试获取物资列表
        print("2. 获取物资列表:")
        materials = material_service.get_material_list(admin_user)
        print(f"   物资数量: {len(materials)}")
        
    except Exception as e:
        print(f"   错误: {e}")
        
    try:
        # 测试获取分配历史
        print("3. 获取分配历史:")
        allocations = material_service.get_allocation_history(admin_user)
        print(f"   分配记录数量: {len(allocations)}")
        
    except Exception as e:
        print(f"   错误: {e}")
        
else:
    print("❌ 管理员用户不存在")
