{% extends "base.html" %}

{% block title %}仪表板 - 金融企业物资管理系统{% endblock %}

{% block content %}
<div class="card">
    <div class="card-header">
        <h1 class="card-title">仪表板</h1>
        <p>欢迎使用物资管理系统，{{ user.real_name }}！</p>
    </div>
    
    <!-- 统计卡片 -->
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-number">{{ stats.total_materials }}</div>
            <div class="stat-label">总物资数量</div>
        </div>
        
        <div class="stat-card">
            <div class="stat-number">{{ stats.fixed_assets }}</div>
            <div class="stat-label">固定资产</div>
        </div>
        
        <div class="stat-card">
            <div class="stat-number">{{ stats.consumables }}</div>
            <div class="stat-label">耗材</div>
        </div>
        
        {% if user.role == 'admin' %}
        <div class="stat-card">
            <div class="stat-number">{{ stats.pending_requests }}</div>
            <div class="stat-label">待审核申请</div>
        </div>
        {% endif %}
    </div>
    
    <!-- 智能体控制台 -->
    <div class="card">
        <div class="card-header">
            <h2 class="card-title">🤖 智能体控制台</h2>
            <p class="text-muted">AI助手为您提供智能化的物资管理服务</p>
        </div>

        <!-- 系统状态 -->
        <div class="row mb-3">
            <div class="col-md-12">
                <div class="d-flex align-items-center">
                    <span class="me-2" id="system-indicator">🟡 系统检查中</span>
                    <span class="badge bg-warning" id="ai-service-badge">检查中</span>
                    <span class="ms-auto text-muted" id="system-status">正在检查系统状态...</span>
                    <a href="{{ url_for('agent.api_config') }}" class="btn btn-sm btn-outline-primary ms-2">
                        <i class="fas fa-cog"></i> API配置
                    </a>
                </div>
            </div>
        </div>

        <!-- 智能体卡片 -->
        <div class="row">
            <div class="col-md-3 mb-3">
                <div class="card agent-card h-100" data-agent="request" onclick="showAgentDetails('request')" style="cursor: pointer;">
                    <div class="card-body text-center">
                        <div class="agent-icon mb-2">👤</div>
                        <h5 class="card-title">智能申请助手</h5>
                        <p class="card-text small">协助用户智能填写申请，预测需求，优化申请理由</p>
                        <div class="agent-status">
                            <span class="badge bg-success">运行正常</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-3 mb-3">
                <div class="card agent-card h-100" data-agent="approval" onclick="showAgentDetails('approval')" style="cursor: pointer;">
                    <div class="card-body text-center">
                        <div class="agent-icon mb-2">✅</div>
                        <h5 class="card-title">智能审批助手</h5>
                        <p class="card-text small">自动分析申请合理性，提供审批建议，风险评估</p>
                        <div class="agent-status">
                            <span class="badge bg-success">运行正常</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-3 mb-3">
                <div class="card agent-card h-100" data-agent="inventory" onclick="showAgentDetails('inventory')" style="cursor: pointer;">
                    <div class="card-body text-center">
                        <div class="agent-icon mb-2">⚙️</div>
                        <h5 class="card-title">智能库存管理</h5>
                        <p class="card-text small">监控库存水平，预测补货需求，优化分配策略</p>
                        <div class="agent-status">
                            <span class="badge bg-success">运行正常</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-3 mb-3">
                <div class="card agent-card h-100" data-agent="report" onclick="showAgentDetails('report')" style="cursor: pointer;">
                    <div class="card-body text-center">
                        <div class="agent-icon mb-2">📊</div>
                        <h5 class="card-title">智能报表分析</h5>
                        <p class="card-text small">自动生成洞察报告，趋势分析，异常检测</p>
                        <div class="agent-status">
                            <span class="badge bg-success">运行正常</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 快速操作 -->
        <div class="row mt-3">
            <div class="col-md-3 mb-2">
                <button class="btn btn-outline-primary w-100" onclick="generateComprehensiveReport()">
                    📋 生成综合报告
                </button>
            </div>
            <div class="col-md-3 mb-2">
                <button class="btn btn-outline-info w-100" onclick="checkInventoryStatus()">
                    📦 检查库存状态
                </button>
            </div>
            <div class="col-md-3 mb-2">
                <button class="btn btn-outline-warning w-100" onclick="detectAnomalies()">
                    ⚠️ 异常检测
                </button>
            </div>
            <div class="col-md-3 mb-2">
                <button class="btn btn-outline-success w-100" onclick="optimizeWorkflow()">
                    🔧 优化工作流程
                </button>
            </div>
        </div>
    </div>

    <!-- 智能对话 -->
    <div class="card">
        <div class="card-header">
            <h2 class="card-title">💬 智能对话</h2>
            <p class="text-muted">与AI助手对话，获取个性化建议</p>
        </div>

        <div class="card-body">
            <div id="chat-messages" class="chat-messages mb-3" style="height: 300px; overflow-y: auto; border: 1px solid #ddd; padding: 10px; border-radius: 5px;">
                <div class="message ai-message">
                    <strong>AI助手:</strong> 您好！我是您的智能物资管理助手。我可以帮您：<br>
                    • 推荐合适的物资<br>
                    • 分析申请合理性<br>
                    • 监控库存状态<br>
                    • 生成分析报告<br>
                    请告诉我您需要什么帮助？
                </div>
            </div>

            <div class="input-group">
                <input type="text" id="chat-input" class="form-control" placeholder="输入您的问题..." onkeypress="handleChatKeyPress(event)">
                <button class="btn btn-primary" onclick="sendChatMessage()">发送</button>
            </div>
        </div>
    </div>

    <!-- 快速操作 -->
    <div class="card">
        <div class="card-header">
            <h2 class="card-title">快速操作</h2>
        </div>

        <div class="d-flex gap-2">
            <a href="{{ url_for('material.material_list') }}" class="btn btn-primary">查看物资</a>
            {% if user.role == 'admin' %}
            <a href="{{ url_for('material.add_material') }}" class="btn btn-success">添加物资</a>
            <a href="{{ url_for('material.request_list') }}" class="btn btn-warning">审核申请</a>
            {% endif %}
            <a href="{{ url_for('report.report_dashboard') }}" class="btn btn-info">查看报表</a>
        </div>
    </div>
    
    <!-- 最近分配记录 -->
    {% if recent_allocations %}
    <div class="card">
        <div class="card-header">
            <h2 class="card-title">最近分配记录</h2>
        </div>
        
        <table class="table">
            <thead>
                <tr>
                    <th>物资名称</th>
                    <th>类别</th>
                    <th>科室</th>
                    <th>领取人</th>
                    <th>数量</th>
                    <th>分配日期</th>
                    <th>状态</th>
                </tr>
            </thead>
            <tbody>
                {% for allocation in recent_allocations %}
                <tr>
                    <td>{{ allocation.material_name or '-' }}</td>
                    <td>
                        {% if allocation.category == 'fixed_asset' %}
                            <span style="color: var(--primary-color);">固定资产</span>
                        {% else %}
                            <span style="color: var(--accent-color);">耗材</span>
                        {% endif %}
                    </td>
                    <td>{{ allocation.department_name or '-' }}</td>
                    <td>{{ allocation.user_name or '-' }}</td>
                    <td>{{ allocation.quantity }}</td>
                    <td>{{ allocation.allocation_date.strftime('%Y-%m-%d') if allocation.allocation_date else '-' }}</td>
                    <td>
                        {% if allocation.status == 'allocated' %}
                            <span style="color: var(--accent-color);">已分配</span>
                        {% elif allocation.status == 'returned' %}
                            <span style="color: var(--info-color);">已归还</span>
                        {% else %}
                            <span style="color: var(--warning-color);">已消耗</span>
                        {% endif %}
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
        
        <div class="text-center mt-2">
            <a href="{{ url_for('material.allocation_history') }}" class="btn btn-primary">查看全部记录</a>
        </div>
    </div>
    {% endif %}
    
    <!-- 待审核申请（仅管理员） -->
    {% if user.role == 'admin' and pending_requests %}
    <div class="card">
        <div class="card-header">
            <h2 class="card-title">待审核申请</h2>
        </div>
        
        <table class="table">
            <thead>
                <tr>
                    <th>物资名称</th>
                    <th>申请人</th>
                    <th>科室</th>
                    <th>数量</th>
                    <th>申请日期</th>
                    <th>申请理由</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody>
                {% for request in pending_requests %}
                <tr>
                    <td>{{ request.material_name or '-' }}</td>
                    <td>{{ request.user_name or '-' }}</td>
                    <td>{{ request.department_name or '-' }}</td>
                    <td>{{ request.quantity }}</td>
                    <td>{{ request.request_date.strftime('%Y-%m-%d') if request.request_date else '-' }}</td>
                    <td>{{ request.reason or '-' }}</td>
                    <td>
                        <form method="POST" action="{{ url_for('material.approve_request', request_id=request.id) }}" style="display: inline;">
                            <button type="submit" class="btn btn-success btn-sm">批准</button>
                        </form>
                        <form method="POST" action="{{ url_for('material.reject_request', request_id=request.id) }}" style="display: inline;">
                            <button type="submit" class="btn btn-danger btn-sm">拒绝</button>
                        </form>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
        
        <div class="text-center mt-2">
            <a href="{{ url_for('material.request_list') }}" class="btn btn-primary">查看全部申请</a>
        </div>
    </div>
    {% endif %}
</div>

<!-- 智能体详情模态框 -->
<div class="modal fade" id="agentModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="agentModalTitle">智能体详情</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="agentModalBody">
                <div class="text-center">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                    <p>正在加载智能体详细信息...</p>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.agent-card {
    cursor: pointer;
    transition: all 0.3s ease;
    border: 1px solid #ddd;
}

.agent-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    border-color: #007bff;
}

.agent-icon {
    font-size: 2rem;
}

.chat-messages {
    background-color: #f8f9fa;
}

.message {
    margin-bottom: 10px;
    padding: 8px 12px;
    border-radius: 8px;
}

.ai-message {
    background-color: #e3f2fd;
    border-left: 4px solid #2196f3;
}

.user-message {
    background-color: #f3e5f5;
    border-left: 4px solid #9c27b0;
    margin-left: 20px;
}

.agent-status .badge {
    font-size: 0.7rem;
}
</style>

<script>
// 智能体相关功能
let currentUser = {{ user|tojson }};

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    checkSystemStatus();

    // 为智能体卡片添加点击事件
    document.querySelectorAll('.agent-card').forEach(card => {
        card.addEventListener('click', function() {
            const agentType = this.dataset.agent;
            showAgentDetails(agentType);
        });
    });
});

// 检查系统状态
function checkSystemStatus() {
    // 检查API配置状态
    fetch('/agent/api_status')
        .then(response => response.json())
        .then(data => {
            const statusElement = document.getElementById('system-status');
            const indicator = document.getElementById('system-indicator');
            const badge = document.getElementById('ai-service-badge');

            if (data.success && data.data.configured) {
                statusElement.textContent = '系统运行正常 - ' + new Date().toLocaleTimeString();
                statusElement.className = 'ms-auto text-success';
                indicator.textContent = '🟢 系统正常';
                badge.textContent = 'AI服务可用';
                badge.className = 'badge bg-success';
            } else {
                statusElement.textContent = 'API未配置 - 请配置DeepSeek API密钥';
                statusElement.className = 'ms-auto text-warning';
                indicator.textContent = '🟡 需要配置';
                badge.textContent = 'API未配置';
                badge.className = 'badge bg-warning';
            }
        })
        .catch(error => {
            console.error('检查系统状态失败:', error);
            const statusElement = document.getElementById('system-status');
            const indicator = document.getElementById('system-indicator');
            const badge = document.getElementById('ai-service-badge');

            statusElement.textContent = '无法连接到智能体服务';
            statusElement.className = 'ms-auto text-danger';
            indicator.textContent = '🔴 连接失败';
            badge.textContent = '服务异常';
            badge.className = 'badge bg-danger';
        });
}

// 显示智能体详情
function showAgentDetails(agentType) {
    const modal = new bootstrap.Modal(document.getElementById('agentModal'));
    const modalTitle = document.getElementById('agentModalTitle');
    const modalBody = document.getElementById('agentModalBody');

    // 设置标题
    const agentNames = {
        'request': '智能申请助手',
        'approval': '智能审批助手',
        'inventory': '智能库存管理',
        'report': '智能报表分析'
    };

    modalTitle.textContent = agentNames[agentType] || '智能体详情';

    // 显示加载状态
    modalBody.innerHTML = `
        <div class="text-center">
            <div class="spinner-border" role="status">
                <span class="visually-hidden">加载中...</span>
            </div>
            <p>正在加载智能体详细信息...</p>
        </div>
    `;

    modal.show();

    // 获取智能体详情
    fetch(`/agent/status?agent=${agentType}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const agentData = data.data;
                modalBody.innerHTML = `
                    <div class="row">
                        <div class="col-md-6">
                            <h6>基本信息</h6>
                            <p><strong>名称:</strong> ${agentData.name}</p>
                            <p><strong>状态:</strong> <span class="badge bg-success">运行正常</span></p>
                            <p><strong>版本:</strong> ${agentData.version || '1.0.0'}</p>
                        </div>
                        <div class="col-md-6">
                            <h6>功能列表</h6>
                            <ul class="list-unstyled">
                                ${agentData.capabilities.map(cap => `<li>• ${cap}</li>`).join('')}
                            </ul>
                        </div>
                    </div>
                    <hr>
                    <div class="row">
                        <div class="col-12">
                            <h6>快速操作</h6>
                            <div class="d-flex gap-2 flex-wrap">
                                ${getAgentActions(agentType)}
                            </div>
                        </div>
                    </div>
                `;
            } else {
                modalBody.innerHTML = `
                    <div class="alert alert-danger">
                        <h6>加载失败</h6>
                        <p>${data.error || '无法获取智能体信息'}</p>
                    </div>
                `;
            }
        })
        .catch(error => {
            console.error('获取智能体详情失败:', error);
            modalBody.innerHTML = `
                <div class="alert alert-danger">
                    <h6>连接失败</h6>
                    <p>无法连接到智能体服务，请检查网络连接。</p>
                </div>
            `;
        });
}

// 获取智能体操作按钮
function getAgentActions(agentType) {
    const actions = {
        'request': [
            '<button class="btn btn-sm btn-primary" onclick="suggestMaterials()">推荐物资</button>',
            '<button class="btn btn-sm btn-info" onclick="predictQuantity()">预测数量</button>'
        ],
        'approval': [
            '<button class="btn btn-sm btn-warning" onclick="analyzeRequests()">分析申请</button>',
            '<button class="btn btn-sm btn-danger" onclick="riskAssessment()">风险评估</button>'
        ],
        'inventory': [
            '<button class="btn btn-sm btn-success" onclick="checkInventoryStatus()">检查库存</button>',
            '<button class="btn btn-sm btn-info" onclick="predictDemand()">需求预测</button>'
        ],
        'report': [
            '<button class="btn btn-sm btn-primary" onclick="generateInsights()">生成洞察</button>',
            '<button class="btn btn-sm btn-secondary" onclick="trendAnalysis()">趋势分析</button>'
        ]
    };

    return actions[agentType]?.join('') || '';
}

// 聊天功能
function handleChatKeyPress(event) {
    if (event.key === 'Enter') {
        sendChatMessage();
    }
}

function sendChatMessage() {
    const input = document.getElementById('chat-input');
    const message = input.value.trim();

    if (!message) return;

    // 显示用户消息
    addChatMessage(message, 'user');
    input.value = '';

    // 显示AI思考状态
    const thinkingId = addChatMessage('正在思考...', 'ai', true);

    // 发送到AI
    fetch('/agent/chat', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            message: message,
            context: {
                user_id: currentUser.id,
                department_id: currentUser.department_id,
                role: currentUser.role
            }
        })
    })
    .then(response => response.json())
    .then(data => {
        // 移除思考状态
        document.getElementById(thinkingId).remove();

        if (data.success) {
            addChatMessage(data.data.response, 'ai');
        } else {
            addChatMessage('抱歉，我现在无法回答您的问题。错误：' + (data.error || '未知错误'), 'ai');
        }
    })
    .catch(error => {
        console.error('发送消息失败:', error);
        document.getElementById(thinkingId).remove();
        addChatMessage('抱歉，网络连接出现问题，请稍后再试。', 'ai');
    });
}

function addChatMessage(message, sender, isTemporary = false) {
    const messagesContainer = document.getElementById('chat-messages');
    const messageDiv = document.createElement('div');
    const messageId = 'msg-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);

    messageDiv.id = messageId;
    messageDiv.className = `message ${sender}-message`;
    messageDiv.innerHTML = `<strong>${sender === 'user' ? '您' : 'AI助手'}:</strong> ${message}`;

    messagesContainer.appendChild(messageDiv);
    messagesContainer.scrollTop = messagesContainer.scrollHeight;

    return isTemporary ? messageId : null;
}

// 快速操作功能
function generateComprehensiveReport() {
    showLoading('正在生成综合报告...');

    fetch('/agent/coordinator/comprehensive_report', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            period_days: 30,
            report_type: 'full'
        })
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();
        if (data.success) {
            showReportModal('综合报告', data.data);
        } else {
            alert('生成报告失败：' + (data.error || '未知错误'));
        }
    })
    .catch(error => {
        hideLoading();
        console.error('生成报告失败:', error);
        alert('生成报告失败，请稍后再试。');
    });
}

function checkInventoryStatus() {
    showLoading('正在检查库存状态...');

    fetch('/agent/inventory/monitor')
    .then(response => response.json())
    .then(data => {
        hideLoading();
        if (data.success) {
            showReportModal('库存状态报告', data.data);
        } else {
            alert('检查库存失败：' + (data.error || '未知错误'));
        }
    })
    .catch(error => {
        hideLoading();
        console.error('检查库存失败:', error);
        alert('检查库存失败，请稍后再试。');
    });
}

function detectAnomalies() {
    showLoading('正在进行异常检测...');

    fetch('/agent/report/anomaly_detection')
    .then(response => response.json())
    .then(data => {
        hideLoading();
        if (data.success) {
            showReportModal('异常检测报告', data.data);
        } else {
            alert('异常检测失败：' + (data.error || '未知错误'));
        }
    })
    .catch(error => {
        hideLoading();
        console.error('异常检测失败:', error);
        alert('异常检测失败，请稍后再试。');
    });
}

function optimizeWorkflow() {
    if (currentUser.role !== 'admin') {
        alert('只有管理员可以执行工作流程优化。');
        return;
    }

    showLoading('正在优化工作流程...');

    fetch('/agent/coordinator/optimize_workflow', {
        method: 'POST'
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();
        if (data.success) {
            showReportModal('工作流程优化建议', data.data);
        } else {
            alert('工作流程优化失败：' + (data.error || '未知错误'));
        }
    })
    .catch(error => {
        hideLoading();
        console.error('工作流程优化失败:', error);
        alert('工作流程优化失败，请稍后再试。');
    });
}

// 辅助函数
function showLoading(message) {
    // 可以在这里显示全局加载状态
    console.log(message);
}

function hideLoading() {
    // 隐藏全局加载状态
}

function showReportModal(title, data) {
    // 显示报告模态框
    alert(title + ':\n' + JSON.stringify(data, null, 2));
}

// 定期更新系统状态
setInterval(checkSystemStatus, 30000); // 每30秒检查一次
</script>
{% endblock %}
