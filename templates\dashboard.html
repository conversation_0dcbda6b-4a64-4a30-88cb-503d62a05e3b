{% extends "base.html" %}

{% block title %}仪表板 - 金融企业物资管理系统{% endblock %}

{% block content %}
<div class="card">
    <div class="card-header">
        <h1 class="card-title">仪表板</h1>
        <p>欢迎使用物资管理系统，{{ user.real_name }}！</p>
    </div>
    
    <!-- 统计卡片 -->
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-number">{{ stats.total_materials }}</div>
            <div class="stat-label">总物资数量</div>
        </div>
        
        <div class="stat-card">
            <div class="stat-number">{{ stats.fixed_assets }}</div>
            <div class="stat-label">固定资产</div>
        </div>
        
        <div class="stat-card">
            <div class="stat-number">{{ stats.consumables }}</div>
            <div class="stat-label">耗材</div>
        </div>
        
        {% if user.role == 'admin' %}
        <div class="stat-card">
            <div class="stat-number">{{ stats.pending_requests }}</div>
            <div class="stat-label">待审核申请</div>
        </div>
        {% endif %}
    </div>
    
    <!-- 快速操作 -->
    <div class="card">
        <div class="card-header">
            <h2 class="card-title">快速操作</h2>
        </div>
        
        <div class="d-flex gap-2">
            <a href="{{ url_for('material.material_list') }}" class="btn btn-primary">查看物资</a>
            {% if user.role == 'admin' %}
            <a href="{{ url_for('material.add_material') }}" class="btn btn-success">添加物资</a>
            <a href="{{ url_for('material.request_list') }}" class="btn btn-warning">审核申请</a>
            {% endif %}
            <a href="{{ url_for('report.report_dashboard') }}" class="btn btn-info">查看报表</a>
        </div>
    </div>
    
    <!-- 最近分配记录 -->
    {% if recent_allocations %}
    <div class="card">
        <div class="card-header">
            <h2 class="card-title">最近分配记录</h2>
        </div>
        
        <table class="table">
            <thead>
                <tr>
                    <th>物资名称</th>
                    <th>类别</th>
                    <th>科室</th>
                    <th>领取人</th>
                    <th>数量</th>
                    <th>分配日期</th>
                    <th>状态</th>
                </tr>
            </thead>
            <tbody>
                {% for allocation in recent_allocations %}
                <tr>
                    <td>{{ allocation.material_name or '-' }}</td>
                    <td>
                        {% if allocation.category == 'fixed_asset' %}
                            <span style="color: var(--primary-color);">固定资产</span>
                        {% else %}
                            <span style="color: var(--accent-color);">耗材</span>
                        {% endif %}
                    </td>
                    <td>{{ allocation.department_name or '-' }}</td>
                    <td>{{ allocation.user_name or '-' }}</td>
                    <td>{{ allocation.quantity }}</td>
                    <td>{{ allocation.allocation_date.strftime('%Y-%m-%d') if allocation.allocation_date else '-' }}</td>
                    <td>
                        {% if allocation.status == 'allocated' %}
                            <span style="color: var(--accent-color);">已分配</span>
                        {% elif allocation.status == 'returned' %}
                            <span style="color: var(--info-color);">已归还</span>
                        {% else %}
                            <span style="color: var(--warning-color);">已消耗</span>
                        {% endif %}
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
        
        <div class="text-center mt-2">
            <a href="{{ url_for('material.allocation_history') }}" class="btn btn-primary">查看全部记录</a>
        </div>
    </div>
    {% endif %}
    
    <!-- 待审核申请（仅管理员） -->
    {% if user.role == 'admin' and pending_requests %}
    <div class="card">
        <div class="card-header">
            <h2 class="card-title">待审核申请</h2>
        </div>
        
        <table class="table">
            <thead>
                <tr>
                    <th>物资名称</th>
                    <th>申请人</th>
                    <th>科室</th>
                    <th>数量</th>
                    <th>申请日期</th>
                    <th>申请理由</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody>
                {% for request in pending_requests %}
                <tr>
                    <td>{{ request.material_name or '-' }}</td>
                    <td>{{ request.user_name or '-' }}</td>
                    <td>{{ request.department_name or '-' }}</td>
                    <td>{{ request.quantity }}</td>
                    <td>{{ request.request_date.strftime('%Y-%m-%d') if request.request_date else '-' }}</td>
                    <td>{{ request.reason or '-' }}</td>
                    <td>
                        <form method="POST" action="{{ url_for('material.approve_request', request_id=request.id) }}" style="display: inline;">
                            <button type="submit" class="btn btn-success btn-sm">批准</button>
                        </form>
                        <form method="POST" action="{{ url_for('material.reject_request', request_id=request.id) }}" style="display: inline;">
                            <button type="submit" class="btn btn-danger btn-sm">拒绝</button>
                        </form>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
        
        <div class="text-center mt-2">
            <a href="{{ url_for('material.request_list') }}" class="btn btn-primary">查看全部申请</a>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}
