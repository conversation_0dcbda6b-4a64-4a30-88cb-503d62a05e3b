#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Agent协调中心
管理多个智能体的协作、任务分发、结果整合
"""

import json
import asyncio
from datetime import datetime
from typing import Dict, List, Any, Optional
from agents.base_agent import BaseAgent, AgentResponse, AgentError
from agents.request_assistant_agent import RequestAssistantAgent
from agents.approval_assistant_agent import ApprovalAssistantAgent
from agents.inventory_manager_agent import InventoryManagerAgent
from agents.report_analyst_agent import ReportAnalystAgent

class AgentCoordinator(BaseAgent):
    """Agent协调中心"""
    
    def __init__(self):
        super().__init__(
            agent_id="agent_coordinator",
            name="Agent协调中心",
            description="管理多个智能体的协作、任务分发、结果整合"
        )
        
        # 初始化各个智能体
        self.agents = {
            'request_assistant': RequestAssistantAgent(),
            'approval_assistant': ApprovalAssistantAgent(),
            'inventory_manager': InventoryManagerAgent(),
            'report_analyst': ReportAnalystAgent()
        }
        
        # 任务队列
        self.task_queue = []
        self.active_tasks = {}
        
        # 协作规则
        self.collaboration_rules = {
            'request_flow': ['request_assistant', 'approval_assistant', 'inventory_manager'],
            'analysis_flow': ['inventory_manager', 'report_analyst'],
            'monitoring_flow': ['inventory_manager', 'approval_assistant', 'report_analyst']
        }
    
    def process(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """处理协调请求"""
        try:
            action = input_data.get('action')
            
            if action == 'coordinate_request_process':
                return self._coordinate_request_process(input_data)
            elif action == 'generate_comprehensive_report':
                return self._generate_comprehensive_report(input_data)
            elif action == 'monitor_system_health':
                return self._monitor_system_health(input_data)
            elif action == 'optimize_workflow':
                return self._optimize_workflow(input_data)
            elif action == 'agent_status':
                return self._get_agent_status(input_data)
            elif action == 'execute_workflow':
                return self._execute_workflow(input_data)
            else:
                raise AgentError(f"不支持的操作: {action}", self.agent_id)
                
        except Exception as e:
            self.logger.error(f"处理请求失败: {e}")
            return AgentResponse(False, message=str(e)).to_dict()
    
    def _coordinate_request_process(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """协调申请处理流程"""
        request_data = input_data.get('request_data', {})
        
        try:
            workflow_results = {}
            
            # 步骤1: 智能申请助手分析
            self.logger.info("步骤1: 智能申请助手分析申请")
            request_analysis = self.agents['request_assistant'].process({
                'action': 'analyze_request',
                **request_data
            })
            workflow_results['request_analysis'] = request_analysis
            
            if not request_analysis.get('success'):
                return AgentResponse(
                    False,
                    message="申请分析失败",
                    data=workflow_results
                ).to_dict()
            
            # 步骤2: 智能审批助手评估
            self.logger.info("步骤2: 智能审批助手评估申请")
            approval_analysis = self.agents['approval_assistant'].process({
                'action': 'analyze_request',
                'request_id': request_data.get('request_id')
            })
            workflow_results['approval_analysis'] = approval_analysis
            
            # 步骤3: 库存管理助手检查库存
            self.logger.info("步骤3: 库存管理助手检查库存")
            inventory_check = self.agents['inventory_manager'].process({
                'action': 'monitor_inventory',
                'material_id': request_data.get('material_id')
            })
            workflow_results['inventory_check'] = inventory_check
            
            # 整合结果并生成最终建议
            final_recommendation = self._integrate_request_results(workflow_results)
            
            return AgentResponse(
                True,
                data={
                    'workflow_results': workflow_results,
                    'final_recommendation': final_recommendation
                },
                message="申请处理流程协调完成",
                confidence=0.9
            ).to_dict()
            
        except Exception as e:
            return AgentResponse(False, message=f"申请流程协调失败: {e}").to_dict()
    
    def _generate_comprehensive_report(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """生成综合报告"""
        report_type = input_data.get('report_type', 'full')
        period_days = input_data.get('period_days', 30)
        
        try:
            comprehensive_report = {
                'report_metadata': {
                    'type': report_type,
                    'period_days': period_days,
                    'generated_at': datetime.now().isoformat(),
                    'generated_by': 'agent_coordinator'
                }
            }
            
            # 库存分析
            self.logger.info("生成库存分析报告")
            inventory_report = self.agents['inventory_manager'].process({
                'action': 'monitor_inventory'
            })
            comprehensive_report['inventory_analysis'] = inventory_report
            
            # 需求预测
            self.logger.info("生成需求预测报告")
            demand_forecast = self.agents['inventory_manager'].process({
                'action': 'predict_demand',
                'days_ahead': period_days
            })
            comprehensive_report['demand_forecast'] = demand_forecast
            
            # 审批分析
            self.logger.info("生成审批分析报告")
            approval_stats = self.agents['approval_assistant'].process({
                'action': 'batch_analyze',
                'request_ids': []  # 获取所有待审核申请
            })
            comprehensive_report['approval_analysis'] = approval_stats
            
            # 趋势分析
            self.logger.info("生成趋势分析报告")
            trend_analysis = self.agents['report_analyst'].process({
                'action': 'trend_analysis',
                'metric': 'requests',
                'period_days': period_days
            })
            comprehensive_report['trend_analysis'] = trend_analysis
            
            # 异常检测
            self.logger.info("执行异常检测")
            anomaly_detection = self.agents['report_analyst'].process({
                'action': 'anomaly_detection'
            })
            comprehensive_report['anomaly_detection'] = anomaly_detection
            
            # 性能分析
            self.logger.info("生成性能分析报告")
            performance_analysis = self.agents['report_analyst'].process({
                'action': 'performance_analysis'
            })
            comprehensive_report['performance_analysis'] = performance_analysis
            
            # 生成执行摘要
            executive_summary = self._generate_executive_summary(comprehensive_report)
            comprehensive_report['executive_summary'] = executive_summary
            
            return AgentResponse(
                True,
                data=comprehensive_report,
                message="综合报告生成完成",
                confidence=0.95
            ).to_dict()
            
        except Exception as e:
            return AgentResponse(False, message=f"综合报告生成失败: {e}").to_dict()
    
    def _monitor_system_health(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """监控系统健康状态"""
        try:
            health_status = {
                'overall_status': 'healthy',
                'agent_status': {},
                'system_metrics': {},
                'alerts': [],
                'recommendations': []
            }
            
            # 检查各个智能体状态
            for agent_id, agent in self.agents.items():
                try:
                    # 测试智能体响应
                    test_response = agent.process({'action': 'health_check'})
                    health_status['agent_status'][agent_id] = {
                        'status': 'healthy' if test_response.get('success', False) else 'unhealthy',
                        'last_activity': agent.last_activity.isoformat() if agent.last_activity else None,
                        'capabilities': agent.get_capabilities()
                    }
                except Exception as e:
                    health_status['agent_status'][agent_id] = {
                        'status': 'error',
                        'error': str(e)
                    }
            
            # 库存健康检查
            inventory_health = self.agents['inventory_manager'].process({
                'action': 'inventory_alert'
            })
            if inventory_health.get('success') and inventory_health.get('data', {}).get('alerts'):
                health_status['alerts'].extend(inventory_health['data']['alerts'])
            
            # 异常检测
            anomaly_check = self.agents['report_analyst'].process({
                'action': 'anomaly_detection'
            })
            if anomaly_check.get('success'):
                anomaly_data = anomaly_check.get('data', {})
                if anomaly_data.get('summary', {}).get('total_anomalies', 0) > 0:
                    health_status['alerts'].append({
                        'level': 'warning',
                        'message': f"检测到 {anomaly_data['summary']['total_anomalies']} 个异常",
                        'source': 'anomaly_detection'
                    })
            
            # 确定整体状态
            unhealthy_agents = [aid for aid, status in health_status['agent_status'].items() 
                              if status['status'] != 'healthy']
            critical_alerts = [alert for alert in health_status['alerts'] 
                             if alert.get('level') == 'critical']
            
            if unhealthy_agents or critical_alerts:
                health_status['overall_status'] = 'unhealthy'
            elif health_status['alerts']:
                health_status['overall_status'] = 'warning'
            
            # 生成建议
            if unhealthy_agents:
                health_status['recommendations'].append(f"检查智能体: {', '.join(unhealthy_agents)}")
            if health_status['alerts']:
                health_status['recommendations'].append("处理系统警报")
            
            return AgentResponse(
                True,
                data=health_status,
                message=f"系统状态: {health_status['overall_status']}",
                confidence=0.95
            ).to_dict()
            
        except Exception as e:
            return AgentResponse(False, message=f"系统健康监控失败: {e}").to_dict()
    
    def _optimize_workflow(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """优化工作流程"""
        workflow_type = input_data.get('workflow_type', 'request_process')
        
        try:
            optimization_results = {
                'workflow_type': workflow_type,
                'current_performance': {},
                'optimization_suggestions': [],
                'estimated_improvements': {}
            }
            
            if workflow_type == 'request_process':
                # 分析当前申请处理性能
                performance_data = self.agents['report_analyst'].process({
                    'action': 'performance_analysis'
                })
                
                if performance_data.get('success'):
                    optimization_results['current_performance'] = performance_data['data']
                    
                    # 生成优化建议
                    perf_metrics = performance_data['data']['performance_metrics']
                    
                    if perf_metrics['request_processing']['score'] < 0.8:
                        optimization_results['optimization_suggestions'].append({
                            'area': 'request_processing',
                            'suggestion': '优化申请处理流程，减少处理时间',
                            'expected_improvement': '20%'
                        })
                    
                    if perf_metrics['approval_efficiency']['score'] < 0.8:
                        optimization_results['optimization_suggestions'].append({
                            'area': 'approval_efficiency',
                            'suggestion': '改进审批流程，提高审批效率',
                            'expected_improvement': '15%'
                        })
            
            return AgentResponse(
                True,
                data=optimization_results,
                message="工作流程优化分析完成",
                confidence=0.8
            ).to_dict()
            
        except Exception as e:
            return AgentResponse(False, message=f"工作流程优化失败: {e}").to_dict()
    
    def _get_agent_status(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """获取智能体状态"""
        try:
            agent_status = {}
            
            for agent_id, agent in self.agents.items():
                agent_status[agent_id] = {
                    'name': agent.name,
                    'description': agent.description,
                    'status': agent.status,
                    'last_activity': agent.last_activity.isoformat() if agent.last_activity else None,
                    'capabilities': agent.get_capabilities(),
                    'activity_count': len(agent.activity_log)
                }
            
            return AgentResponse(
                True,
                data=agent_status,
                message="智能体状态获取完成",
                confidence=1.0
            ).to_dict()
            
        except Exception as e:
            return AgentResponse(False, message=f"获取智能体状态失败: {e}").to_dict()
    
    def _execute_workflow(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """执行工作流程"""
        workflow_name = input_data.get('workflow_name')
        workflow_data = input_data.get('workflow_data', {})
        
        try:
            if workflow_name not in self.collaboration_rules:
                return AgentResponse(
                    False,
                    message=f"未知的工作流程: {workflow_name}"
                ).to_dict()
            
            agent_sequence = self.collaboration_rules[workflow_name]
            workflow_results = {}
            
            for agent_id in agent_sequence:
                if agent_id in self.agents:
                    self.logger.info(f"执行智能体: {agent_id}")
                    
                    # 根据智能体类型调整输入数据
                    agent_input = self._prepare_agent_input(agent_id, workflow_data, workflow_results)
                    
                    result = self.agents[agent_id].process(agent_input)
                    workflow_results[agent_id] = result
                    
                    # 如果某个步骤失败，停止工作流程
                    if not result.get('success'):
                        break
            
            return AgentResponse(
                True,
                data=workflow_results,
                message=f"工作流程 {workflow_name} 执行完成",
                confidence=0.9
            ).to_dict()
            
        except Exception as e:
            return AgentResponse(False, message=f"工作流程执行失败: {e}").to_dict()
    
    def get_capabilities(self) -> List[str]:
        """获取协调中心能力列表"""
        return [
            "申请流程协调",
            "综合报告生成",
            "系统健康监控",
            "工作流程优化",
            "智能体状态管理",
            "工作流程执行"
        ]
    
    # 辅助方法
    def _integrate_request_results(self, workflow_results):
        """整合申请处理结果"""
        final_recommendation = {
            'action': 'approve',  # approve, reject, review
            'confidence': 0.5,
            'reasons': [],
            'conditions': []
        }
        
        # 分析各个智能体的建议
        if workflow_results.get('approval_analysis', {}).get('success'):
            approval_data = workflow_results['approval_analysis']['data']
            recommendation = approval_data.get('recommendation', {})
            
            final_recommendation['action'] = recommendation.get('action', 'review')
            final_recommendation['confidence'] = recommendation.get('confidence', 0.5)
            final_recommendation['reasons'].extend(recommendation.get('suggestions', []))
        
        if workflow_results.get('inventory_check', {}).get('success'):
            inventory_data = workflow_results['inventory_check']['data']
            if inventory_data.get('summary', {}).get('critical_count', 0) > 0:
                final_recommendation['conditions'].append('需要优先补货')
        
        return final_recommendation
    
    def _generate_executive_summary(self, comprehensive_report):
        """生成执行摘要"""
        summary = {
            'key_findings': [],
            'critical_issues': [],
            'recommendations': [],
            'next_actions': []
        }
        
        # 从各个报告中提取关键信息
        if comprehensive_report.get('inventory_analysis', {}).get('success'):
            inventory_data = comprehensive_report['inventory_analysis']['data']
            critical_count = inventory_data.get('summary', {}).get('critical_count', 0)
            if critical_count > 0:
                summary['critical_issues'].append(f"{critical_count} 个物资库存紧急")
                summary['next_actions'].append("立即安排紧急物资补货")
        
        if comprehensive_report.get('anomaly_detection', {}).get('success'):
            anomaly_data = comprehensive_report['anomaly_detection']['data']
            total_anomalies = anomaly_data.get('summary', {}).get('total_anomalies', 0)
            if total_anomalies > 0:
                summary['key_findings'].append(f"检测到 {total_anomalies} 个异常")
        
        summary['recommendations'].append("定期监控系统状态")
        summary['recommendations'].append("优化库存管理策略")
        
        return summary
    
    def _prepare_agent_input(self, agent_id, workflow_data, previous_results):
        """为智能体准备输入数据"""
        # 根据智能体类型和之前的结果准备输入
        base_input = workflow_data.copy()
        
        if agent_id == 'request_assistant':
            base_input['action'] = 'analyze_request'
        elif agent_id == 'approval_assistant':
            base_input['action'] = 'analyze_request'
        elif agent_id == 'inventory_manager':
            base_input['action'] = 'monitor_inventory'
        elif agent_id == 'report_analyst':
            base_input['action'] = 'generate_insights'
        
        return base_input
