#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能申请助手Agent
帮助用户智能填写申请、预测需求、优化申请理由
"""

import re
import json
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from agents.base_agent import BaseAgent, AgentResponse, AgentError
from dao.material_dao import MaterialDAO
from dao.allocation_dao import AllocationDAO, RequestDAO
from dao.user_dao import UserDAO
from dao.department_dao import DepartmentDAO

class RequestAssistantAgent(BaseAgent):
    """智能申请助手"""
    
    def __init__(self):
        super().__init__(
            agent_id="request_assistant",
            name="智能申请助手",
            description="帮助用户智能填写物资申请，提供需求预测和申请理由优化建议"
        )
        self.material_dao = MaterialDAO()
        self.allocation_dao = AllocationDAO()
        self.request_dao = RequestDAO()
        self.user_dao = UserDAO()
        self.department_dao = DepartmentDAO()
        
        # 常见申请理由模板
        self.reason_templates = {
            'office_supplies': [
                "日常办公使用，提高工作效率",
                "部门会议记录和文档整理需要",
                "客户资料整理和归档使用",
                "月度报表制作和打印需要"
            ],
            'equipment': [
                "提升工作效率，满足业务发展需要",
                "现有设备老化，需要更新换代",
                "新项目启动，需要配备相应设备",
                "保障工作正常进行，提高服务质量"
            ],
            'consumables': [
                "日常消耗品补充，维持正常运营",
                "季度库存补充，确保供应充足",
                "特殊项目需要，临时增加用量",
                "预防性储备，避免断货影响工作"
            ]
        }
    
    def process(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """处理申请助手请求"""
        try:
            action = input_data.get('action')
            user_id = input_data.get('user_id')
            
            if action == 'suggest_materials':
                return self._suggest_materials(input_data)
            elif action == 'predict_quantity':
                return self._predict_quantity(input_data)
            elif action == 'optimize_reason':
                return self._optimize_reason(input_data)
            elif action == 'validate_request':
                return self._validate_request(input_data)
            elif action == 'get_usage_history':
                return self._get_usage_history(input_data)
            else:
                raise AgentError(f"不支持的操作: {action}", self.agent_id)
                
        except Exception as e:
            self.logger.error(f"处理请求失败: {e}")
            return AgentResponse(False, message=str(e)).to_dict()
    
    def _suggest_materials(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """根据用户历史和部门需求推荐物资"""
        user_id = input_data.get('user_id')
        department_id = input_data.get('department_id')
        category = input_data.get('category')
        
        try:
            # 获取用户历史申请
            user_history = self.request_dao.get_requests_by_user(user_id)
            
            # 获取部门热门物资
            dept_popular = self.allocation_dao.get_popular_materials_by_department(department_id)
            
            # 获取可用物资
            available_materials = self.material_dao.get_available_materials(category)
            
            suggestions = []
            
            # 基于历史申请推荐
            for material in available_materials:
                score = 0
                
                # 用户历史偏好
                user_requests = [r for r in user_history if r.get('material_id') == material.get('id')]
                if user_requests:
                    score += len(user_requests) * 2
                
                # 部门热门度
                if material.get('id') in [m.get('material_id') for m in dept_popular]:
                    score += 3
                
                # 库存充足度
                if material.get('remaining_quantity', 0) > 10:
                    score += 1
                
                if score > 0:
                    suggestions.append({
                        'material': material,
                        'score': score,
                        'reason': self._generate_suggestion_reason(material, user_requests, dept_popular)
                    })
            
            # 按分数排序
            suggestions.sort(key=lambda x: x['score'], reverse=True)
            
            return AgentResponse(
                True,
                data=suggestions[:10],
                message="基于您的历史记录和部门需求为您推荐以下物资",
                confidence=0.8
            ).to_dict()
            
        except Exception as e:
            return AgentResponse(False, message=f"推荐物资失败: {e}").to_dict()
    
    def _predict_quantity(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """预测申请数量"""
        user_id = input_data.get('user_id')
        material_id = input_data.get('material_id')
        
        try:
            # 获取用户对该物资的历史申请
            user_history = self.request_dao.get_user_material_history(user_id, material_id)
            
            if not user_history:
                # 没有历史记录，使用部门平均值
                dept_avg = self.request_dao.get_department_material_average(
                    input_data.get('department_id'), material_id
                )
                predicted_quantity = max(1, int(dept_avg or 1))
                confidence = 0.5
            else:
                # 基于历史记录预测
                quantities = [r.get('quantity', 1) for r in user_history]
                predicted_quantity = int(sum(quantities) / len(quantities))
                confidence = min(0.9, 0.5 + len(quantities) * 0.1)
            
            # 考虑季节性因素
            current_month = datetime.now().month
            seasonal_factor = self._get_seasonal_factor(material_id, current_month)
            predicted_quantity = max(1, int(predicted_quantity * seasonal_factor))
            
            return AgentResponse(
                True,
                data={
                    'predicted_quantity': predicted_quantity,
                    'historical_average': sum(quantities) / len(quantities) if user_history else None,
                    'seasonal_factor': seasonal_factor,
                    'recommendation': f"建议申请数量: {predicted_quantity}"
                },
                message=f"基于历史数据预测，建议申请数量为 {predicted_quantity}",
                confidence=confidence
            ).to_dict()
            
        except Exception as e:
            return AgentResponse(False, message=f"预测数量失败: {e}").to_dict()
    
    def _optimize_reason(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """优化申请理由"""
        material_id = input_data.get('material_id')
        original_reason = input_data.get('reason', '')
        user_id = input_data.get('user_id')
        
        try:
            # 获取物资信息
            material = self.material_dao.get_material_by_id(material_id)
            if not material:
                return AgentResponse(False, message="物资不存在").to_dict()
            
            # 分析物资类别
            category = material.category
            
            # 获取模板建议
            templates = self.reason_templates.get(category, self.reason_templates['office_supplies'])
            
            # 分析原始理由
            optimized_reasons = []
            
            if original_reason:
                # 改进原始理由
                improved_reason = self._improve_reason(original_reason, material)
                optimized_reasons.append({
                    'type': 'improved',
                    'reason': improved_reason,
                    'score': 0.9
                })
            
            # 添加模板建议
            for template in templates:
                customized = self._customize_template(template, material, user_id)
                optimized_reasons.append({
                    'type': 'template',
                    'reason': customized,
                    'score': 0.7
                })
            
            return AgentResponse(
                True,
                data=optimized_reasons,
                message="为您优化了申请理由，选择最合适的一个",
                suggestions=[r['reason'] for r in optimized_reasons[:3]],
                confidence=0.8
            ).to_dict()
            
        except Exception as e:
            return AgentResponse(False, message=f"优化理由失败: {e}").to_dict()
    
    def _validate_request(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """验证申请的合理性"""
        material_id = input_data.get('material_id')
        quantity = input_data.get('quantity', 1)
        user_id = input_data.get('user_id')
        
        try:
            validation_result = {
                'is_valid': True,
                'warnings': [],
                'suggestions': [],
                'risk_level': 'low'
            }
            
            # 检查物资可用性
            material = self.material_dao.get_material_by_id(material_id)
            if not material:
                validation_result['is_valid'] = False
                validation_result['warnings'].append("物资不存在")
                return AgentResponse(False, data=validation_result).to_dict()
            
            # 检查库存充足性
            if material.remaining_quantity < quantity:
                validation_result['warnings'].append(f"库存不足，当前剩余: {material.remaining_quantity}")
                validation_result['risk_level'] = 'high'
            
            # 检查申请频率
            recent_requests = self.request_dao.get_user_recent_requests(user_id, days=30)
            same_material_requests = [r for r in recent_requests if r.get('material_id') == material_id]
            
            if len(same_material_requests) > 3:
                validation_result['warnings'].append("近期申请该物资过于频繁")
                validation_result['risk_level'] = 'medium'
            
            # 检查数量合理性
            avg_quantity = self._get_average_request_quantity(material_id)
            if quantity > avg_quantity * 2:
                validation_result['warnings'].append(f"申请数量偏大，平均申请量为: {avg_quantity}")
                validation_result['suggestions'].append(f"建议申请数量: {int(avg_quantity)}")
                validation_result['risk_level'] = 'medium'
            
            return AgentResponse(
                True,
                data=validation_result,
                message="申请验证完成" if validation_result['is_valid'] else "申请存在问题",
                confidence=0.9
            ).to_dict()
            
        except Exception as e:
            return AgentResponse(False, message=f"验证申请失败: {e}").to_dict()
    
    def _get_usage_history(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """获取使用历史分析"""
        user_id = input_data.get('user_id')
        material_id = input_data.get('material_id')
        
        try:
            # 获取用户历史
            user_history = self.request_dao.get_user_material_history(user_id, material_id)
            
            # 获取部门历史
            dept_history = self.request_dao.get_department_material_history(
                input_data.get('department_id'), material_id
            )
            
            analysis = {
                'user_total_requests': len(user_history),
                'user_total_quantity': sum(r.get('quantity', 0) for r in user_history),
                'user_avg_quantity': 0,
                'dept_avg_quantity': 0,
                'usage_trend': 'stable',
                'last_request_date': None
            }
            
            if user_history:
                analysis['user_avg_quantity'] = analysis['user_total_quantity'] / len(user_history)
                analysis['last_request_date'] = max(r.get('request_date') for r in user_history)
                
                # 分析趋势
                if len(user_history) >= 3:
                    recent_avg = sum(r.get('quantity', 0) for r in user_history[-3:]) / 3
                    early_avg = sum(r.get('quantity', 0) for r in user_history[:3]) / 3
                    
                    if recent_avg > early_avg * 1.2:
                        analysis['usage_trend'] = 'increasing'
                    elif recent_avg < early_avg * 0.8:
                        analysis['usage_trend'] = 'decreasing'
            
            if dept_history:
                analysis['dept_avg_quantity'] = sum(r.get('quantity', 0) for r in dept_history) / len(dept_history)
            
            return AgentResponse(
                True,
                data=analysis,
                message="使用历史分析完成",
                confidence=0.9
            ).to_dict()
            
        except Exception as e:
            return AgentResponse(False, message=f"获取使用历史失败: {e}").to_dict()
    
    def get_capabilities(self) -> List[str]:
        """获取智能体能力列表"""
        return [
            "物资推荐",
            "数量预测", 
            "理由优化",
            "申请验证",
            "使用历史分析"
        ]
    
    def _generate_suggestion_reason(self, material, user_requests, dept_popular):
        """生成推荐理由"""
        if user_requests:
            return "基于您的使用历史推荐"
        elif material.get('id') in [m.get('material_id') for m in dept_popular]:
            return "部门热门物资"
        else:
            return "库存充足，可申请"
    
    def _get_seasonal_factor(self, material_id, month):
        """获取季节性因子"""
        # 简单的季节性调整
        if month in [1, 2, 12]:  # 冬季
            return 1.1
        elif month in [6, 7, 8]:  # 夏季
            return 0.9
        else:
            return 1.0
    
    def _improve_reason(self, original_reason, material):
        """改进申请理由"""
        if len(original_reason) < 10:
            return f"{original_reason}，用于{material.name}的日常使用，提高工作效率"
        return original_reason
    
    def _customize_template(self, template, material, user_id):
        """定制化模板"""
        return template.replace("物资", material.name)
    
    def _get_average_request_quantity(self, material_id):
        """获取平均申请数量"""
        try:
            avg = self.request_dao.get_material_average_quantity(material_id)
            return max(1, int(avg or 1))
        except:
            return 1
