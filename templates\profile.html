{% extends "base.html" %}

{% block title %}个人信息 - 金融企业物资管理系统{% endblock %}

{% block content %}
<div class="card">
    <div class="card-header">
        <h1 class="card-title">个人信息</h1>
    </div>
    
    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 2rem;">
        <div>
            <h3>基本信息</h3>
            <table class="table">
                <tr>
                    <td><strong>用户名</strong></td>
                    <td>{{ user.username }}</td>
                </tr>
                <tr>
                    <td><strong>姓名</strong></td>
                    <td>{{ user.real_name }}</td>
                </tr>
                <tr>
                    <td><strong>角色</strong></td>
                    <td>
                        {% if user.role == 'admin' %}
                            <span style="color: var(--primary-color); font-weight: bold;">管理员</span>
                        {% else %}
                            <span style="color: var(--info-color); font-weight: bold;">员工</span>
                        {% endif %}
                    </td>
                </tr>
                <tr>
                    <td><strong>科室</strong></td>
                    <td>{{ user.department_name or '-' }}</td>
                </tr>
                <tr>
                    <td><strong>状态</strong></td>
                    <td>
                        {% if user.status == 'active' %}
                            <span style="color: var(--accent-color);">活跃</span>
                        {% else %}
                            <span style="color: var(--danger-color);">禁用</span>
                        {% endif %}
                    </td>
                </tr>
                <tr>
                    <td><strong>创建时间</strong></td>
                    <td>{{ user.created_at if user.created_at else '-' }}</td>
                </tr>
                <tr>
                    <td><strong>更新时间</strong></td>
                    <td>{{ user.updated_at if user.updated_at else '-' }}</td>
                </tr>
            </table>
        </div>
        
        <div>
            <h3>修改密码</h3>
            <form method="POST" action="{{ url_for('auth.change_password') }}">
                <div class="form-group">
                    <label for="old_password" class="form-label">当前密码</label>
                    <input type="password" id="old_password" name="old_password" class="form-control" required>
                </div>
                
                <div class="form-group">
                    <label for="new_password" class="form-label">新密码</label>
                    <input type="password" id="new_password" name="new_password" class="form-control" required>
                </div>
                
                <div class="form-group">
                    <label for="confirm_password" class="form-label">确认新密码</label>
                    <input type="password" id="confirm_password" name="confirm_password" class="form-control" required>
                </div>
                
                <button type="submit" class="btn btn-primary">修改密码</button>
            </form>
        </div>
    </div>
</div>

<script>
// 密码确认验证
document.getElementById('confirm_password').addEventListener('input', function() {
    const newPassword = document.getElementById('new_password').value;
    const confirmPassword = this.value;
    
    if (newPassword !== confirmPassword) {
        this.setCustomValidity('密码不匹配');
    } else {
        this.setCustomValidity('');
    }
});
</script>
{% endblock %}
