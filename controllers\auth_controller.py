from flask import Blueprint, request, render_template, redirect, url_for, session, flash, jsonify
from services.auth_service import AuthService
from functools import wraps

auth_bp = Blueprint('auth', __name__)
auth_service = AuthService()

def login_required(f):
    """登录验证装饰器"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session:
            return redirect(url_for('auth.login'))
        return f(*args, **kwargs)
    return decorated_function

def admin_required(f):
    """管理员权限验证装饰器"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session:
            return redirect(url_for('auth.login'))
        
        user_info = auth_service.get_user_info(session['user_id'])
        if not user_info or user_info['role'] != 'admin':
            flash('需要管理员权限', 'error')
            return redirect(url_for('material.dashboard'))
        return f(*args, **kwargs)
    return decorated_function

@auth_bp.route('/')
def index():
    """首页"""
    if 'user_id' in session:
        return redirect(url_for('material.dashboard'))
    return redirect(url_for('auth.login'))

@auth_bp.route('/login', methods=['GET', 'POST'])
def login():
    """用户登录"""
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        
        if not username or not password:
            flash('请输入用户名和密码', 'error')
            return render_template('login.html')
        
        try:
            user = auth_service.authenticate(username, password)
            if user:
                session['user_id'] = user.id
                session['username'] = user.username
                session['real_name'] = user.real_name
                session['role'] = user.role
                session['department_id'] = user.department_id
                
                flash(f'欢迎回来，{user.real_name}！', 'success')
                return redirect(url_for('material.dashboard'))
            else:
                flash('用户名或密码错误', 'error')
        except Exception as e:
            flash(f'登录失败：{str(e)}', 'error')
    
    return render_template('login.html')

@auth_bp.route('/logout')
def logout():
    """用户登出"""
    session.clear()
    flash('已成功登出', 'info')
    return redirect(url_for('auth.login'))

@auth_bp.route('/profile')
@login_required
def profile():
    """用户个人信息"""
    user_info = auth_service.get_user_info(session['user_id'])
    return render_template('profile.html', user=user_info)

@auth_bp.route('/change_password', methods=['POST'])
@login_required
def change_password():
    """修改密码"""
    old_password = request.form.get('old_password')
    new_password = request.form.get('new_password')
    confirm_password = request.form.get('confirm_password')
    
    if not all([old_password, new_password, confirm_password]):
        flash('请填写所有密码字段', 'error')
        return redirect(url_for('auth.profile'))
    
    if new_password != confirm_password:
        flash('新密码两次输入不一致', 'error')
        return redirect(url_for('auth.profile'))
    
    try:
        auth_service.change_password(session['user_id'], old_password, new_password)
        flash('密码修改成功', 'success')
    except Exception as e:
        flash(f'密码修改失败：{str(e)}', 'error')
    
    return redirect(url_for('auth.profile'))

def get_current_user():
    """获取当前登录用户"""
    if 'user_id' in session:
        return auth_service.get_user_info(session['user_id'])
    return None
