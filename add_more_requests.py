#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pymysql
from datetime import datetime, date

def add_more_requests():
    """添加更多待审核申请"""
    # 数据库连接配置
    config = {
        'host': 'localhost',
        'user': 'root',
        'password': 'qyf20031211',
        'database': 'goods',
        'charset': 'utf8mb4'
    }
    
    try:
        # 连接数据库
        connection = pymysql.connect(**config)
        cursor = connection.cursor()
        
        print("添加更多待审核申请...")
        
        # 获取物资和用户信息
        cursor.execute("SELECT id, name FROM materials WHERE remaining_quantity > 0 LIMIT 15")
        materials = cursor.fetchall()
        
        cursor.execute("SELECT id, real_name, department_id FROM users WHERE role = 'employee'")
        employees = cursor.fetchall()
        
        if materials and employees:
            # 创建更多待审核申请
            new_requests = [
                (materials[0][0], employees[0][0], employees[0][2], 25, '部门会议准备', 'pending', '2024-07-01'),
                (materials[1][0], employees[1][0], employees[1][2], 40, '季度报告制作', 'pending', '2024-07-02'),
                (materials[2][0], employees[2][0], employees[2][2], 15, '客户资料整理', 'pending', '2024-07-03'),
                (materials[3][0], employees[3][0], employees[3][2], 35, '培训材料准备', 'pending', '2024-07-04'),
                (materials[4][0], employees[0][0], employees[0][2], 20, '项目文档打印', 'pending', '2024-07-05'),
                (materials[5][0], employees[1][0], employees[1][2], 30, '合同文件复印', 'pending', '2024-07-06'),
                (materials[6][0], employees[2][0], employees[2][2], 12, '办公用品补充', 'pending', '2024-07-07'),
                (materials[7][0], employees[3][0], employees[3][2], 18, '档案管理需要', 'pending', '2024-07-08'),
                (materials[8][0], employees[0][0], employees[0][2], 22, '月度统计报表', 'pending', '2024-07-09'),
                (materials[9][0], employees[1][0], employees[1][2], 28, '业务流程文档', 'pending', '2024-07-10'),
                (materials[10][0], employees[2][0], employees[2][2], 16, '会议记录整理', 'pending', '2024-07-11'),
                (materials[11][0], employees[3][0], employees[3][2], 24, '年度总结材料', 'pending', '2024-07-12'),
                (materials[12][0], employees[0][0], employees[0][2], 32, '新员工培训资料', 'pending', '2024-07-13'),
                (materials[13][0], employees[1][0], employees[1][2], 14, '部门活动准备', 'pending', '2024-07-14'),
                (materials[14][0], employees[2][0], employees[2][2], 26, '质量管理文档', 'pending', '2024-07-15'),
            ]
            
            for request in new_requests:
                cursor.execute("""
                    INSERT INTO material_requests (material_id, user_id, department_id, quantity, reason, status, request_date)
                    VALUES (%s, %s, %s, %s, %s, %s, %s)
                """, request)
            
            connection.commit()
            print(f"✅ 成功添加 {len(new_requests)} 个待审核申请！")
            
            # 显示当前待审核申请总数
            cursor.execute("SELECT COUNT(*) FROM material_requests WHERE status = 'pending'")
            total_pending = cursor.fetchone()[0]
            print(f"当前待审核申请总数: {total_pending}")
            
        else:
            print("❌ 没有找到足够的物资或用户数据")
        
        cursor.close()
        connection.close()
        
    except Exception as e:
        print(f"❌ 添加申请失败: {e}")
        raise

if __name__ == "__main__":
    add_more_requests()
