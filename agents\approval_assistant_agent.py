#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能审批助手Agent
自动分析申请合理性、提供审批建议、风险评估
"""

import json
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from agents.base_agent import BaseAgent, AgentResponse, AgentError
from dao.material_dao import MaterialDAO
from dao.allocation_dao import AllocationDAO, RequestDAO
from dao.user_dao import UserDAO
from dao.department_dao import DepartmentDAO

class ApprovalAssistantAgent(BaseAgent):
    """智能审批助手"""
    
    def __init__(self):
        super().__init__(
            agent_id="approval_assistant",
            name="智能审批助手", 
            description="自动分析申请合理性，提供审批建议和风险评估"
        )
        self.material_dao = MaterialDAO()
        self.allocation_dao = AllocationDAO()
        self.request_dao = RequestDAO()
        self.user_dao = UserDAO()
        self.department_dao = DepartmentDAO()
        
        # 风险评估权重
        self.risk_weights = {
            'quantity_risk': 0.3,      # 数量风险
            'frequency_risk': 0.25,    # 频率风险
            'inventory_risk': 0.2,     # 库存风险
            'user_history_risk': 0.15, # 用户历史风险
            'department_risk': 0.1     # 部门风险
        }
    
    def process(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """处理审批助手请求"""
        try:
            action = input_data.get('action')
            
            if action == 'analyze_request':
                return self._analyze_request(input_data)
            elif action == 'batch_analyze':
                return self._batch_analyze(input_data)
            elif action == 'risk_assessment':
                return self._risk_assessment(input_data)
            elif action == 'approval_suggestion':
                return self._approval_suggestion(input_data)
            elif action == 'fraud_detection':
                return self._fraud_detection(input_data)
            else:
                raise AgentError(f"不支持的操作: {action}", self.agent_id)
                
        except Exception as e:
            self.logger.error(f"处理请求失败: {e}")
            return AgentResponse(False, message=str(e)).to_dict()
    
    def _analyze_request(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """分析单个申请"""
        request_id = input_data.get('request_id')
        
        try:
            # 获取申请详情
            request_info = self.request_dao.get_request_by_id(request_id)
            if not request_info:
                return AgentResponse(False, message="申请不存在").to_dict()
            
            # 获取相关信息
            material = self.material_dao.get_material_by_id(request_info['material_id'])
            user = self.user_dao.get_user_by_id(request_info['user_id'])
            department = self.department_dao.get_department_by_id(request_info['department_id'])
            
            # 执行各项分析
            quantity_analysis = self._analyze_quantity(request_info, material)
            frequency_analysis = self._analyze_frequency(request_info, user)
            inventory_analysis = self._analyze_inventory(request_info, material)
            user_analysis = self._analyze_user_history(request_info, user)
            department_analysis = self._analyze_department_pattern(request_info, department)
            
            # 综合风险评估
            risk_score = self._calculate_risk_score({
                'quantity': quantity_analysis['risk_level'],
                'frequency': frequency_analysis['risk_level'],
                'inventory': inventory_analysis['risk_level'],
                'user_history': user_analysis['risk_level'],
                'department': department_analysis['risk_level']
            })
            
            # 生成建议
            recommendation = self._generate_recommendation(risk_score, {
                'quantity': quantity_analysis,
                'frequency': frequency_analysis,
                'inventory': inventory_analysis,
                'user_history': user_analysis,
                'department': department_analysis
            })
            
            analysis_result = {
                'request_info': request_info,
                'material_info': material.__dict__ if material else None,
                'user_info': {
                    'id': user.id,
                    'real_name': user.real_name,
                    'department': department.name if department else None
                } if user else None,
                'analysis': {
                    'quantity': quantity_analysis,
                    'frequency': frequency_analysis,
                    'inventory': inventory_analysis,
                    'user_history': user_analysis,
                    'department': department_analysis
                },
                'risk_score': risk_score,
                'recommendation': recommendation
            }
            
            return AgentResponse(
                True,
                data=analysis_result,
                message=f"申请分析完成，风险等级: {self._get_risk_level_text(risk_score)}",
                confidence=0.85
            ).to_dict()
            
        except Exception as e:
            return AgentResponse(False, message=f"分析申请失败: {e}").to_dict()
    
    def _analyze_quantity(self, request_info, material):
        """分析申请数量合理性"""
        quantity = request_info['quantity']
        material_id = request_info['material_id']
        
        # 获取历史平均申请量
        avg_quantity = self.request_dao.get_material_average_quantity(material_id)
        
        analysis = {
            'requested_quantity': quantity,
            'average_quantity': avg_quantity,
            'ratio': quantity / max(avg_quantity, 1),
            'risk_level': 0
        }
        
        # 风险评估
        if analysis['ratio'] > 3:
            analysis['risk_level'] = 0.8
            analysis['warning'] = "申请数量远超平均值"
        elif analysis['ratio'] > 2:
            analysis['risk_level'] = 0.6
            analysis['warning'] = "申请数量偏高"
        elif analysis['ratio'] > 1.5:
            analysis['risk_level'] = 0.3
            analysis['warning'] = "申请数量略高"
        else:
            analysis['risk_level'] = 0.1
            analysis['warning'] = "申请数量正常"
        
        return analysis
    
    def _analyze_frequency(self, request_info, user):
        """分析申请频率"""
        user_id = request_info['user_id']
        material_id = request_info['material_id']
        
        # 获取近期申请
        recent_requests = self.request_dao.get_user_recent_requests(user_id, days=30)
        same_material_requests = [r for r in recent_requests if r['material_id'] == material_id]
        
        analysis = {
            'recent_total_requests': len(recent_requests),
            'same_material_requests': len(same_material_requests),
            'risk_level': 0
        }
        
        # 频率风险评估
        if len(same_material_requests) > 5:
            analysis['risk_level'] = 0.9
            analysis['warning'] = "同一物资申请过于频繁"
        elif len(same_material_requests) > 3:
            analysis['risk_level'] = 0.6
            analysis['warning'] = "同一物资申请较频繁"
        elif len(recent_requests) > 10:
            analysis['risk_level'] = 0.4
            analysis['warning'] = "总申请次数较多"
        else:
            analysis['risk_level'] = 0.1
            analysis['warning'] = "申请频率正常"
        
        return analysis
    
    def _analyze_inventory(self, request_info, material):
        """分析库存影响"""
        quantity = request_info['quantity']
        
        analysis = {
            'current_inventory': material.remaining_quantity if material else 0,
            'requested_quantity': quantity,
            'remaining_after_approval': 0,
            'risk_level': 0
        }
        
        if material:
            analysis['remaining_after_approval'] = material.remaining_quantity - quantity
            
            # 库存风险评估
            if analysis['remaining_after_approval'] < 0:
                analysis['risk_level'] = 1.0
                analysis['warning'] = "库存不足，无法满足申请"
            elif analysis['remaining_after_approval'] < material.remaining_quantity * 0.1:
                analysis['risk_level'] = 0.8
                analysis['warning'] = "批准后库存将严重不足"
            elif analysis['remaining_after_approval'] < material.remaining_quantity * 0.2:
                analysis['risk_level'] = 0.5
                analysis['warning'] = "批准后库存偏低"
            else:
                analysis['risk_level'] = 0.1
                analysis['warning'] = "库存充足"
        else:
            analysis['risk_level'] = 1.0
            analysis['warning'] = "物资不存在"
        
        return analysis
    
    def _analyze_user_history(self, request_info, user):
        """分析用户历史行为"""
        user_id = request_info['user_id']
        
        # 获取用户历史统计
        user_stats = self.request_dao.get_user_request_statistics(user_id)
        
        analysis = {
            'total_requests': user_stats.get('total_requests', 0),
            'approved_requests': user_stats.get('approved_requests', 0),
            'rejected_requests': user_stats.get('rejected_requests', 0),
            'approval_rate': 0,
            'risk_level': 0
        }
        
        if analysis['total_requests'] > 0:
            analysis['approval_rate'] = analysis['approved_requests'] / analysis['total_requests']
            
            # 用户信誉评估
            if analysis['approval_rate'] < 0.5:
                analysis['risk_level'] = 0.8
                analysis['warning'] = "用户历史审批通过率较低"
            elif analysis['approval_rate'] < 0.7:
                analysis['risk_level'] = 0.4
                analysis['warning'] = "用户历史审批通过率一般"
            else:
                analysis['risk_level'] = 0.1
                analysis['warning'] = "用户历史记录良好"
        else:
            analysis['risk_level'] = 0.3
            analysis['warning'] = "新用户，无历史记录"
        
        return analysis
    
    def _analyze_department_pattern(self, request_info, department):
        """分析部门申请模式"""
        department_id = request_info['department_id']
        material_id = request_info['material_id']
        
        # 获取部门统计
        dept_stats = self.request_dao.get_department_material_statistics(department_id, material_id)
        
        analysis = {
            'department_total_requests': dept_stats.get('total_requests', 0),
            'department_avg_quantity': dept_stats.get('avg_quantity', 0),
            'risk_level': 0
        }
        
        # 部门模式分析
        if analysis['department_total_requests'] == 0:
            analysis['risk_level'] = 0.4
            analysis['warning'] = "部门首次申请此物资"
        elif request_info['quantity'] > analysis['department_avg_quantity'] * 2:
            analysis['risk_level'] = 0.6
            analysis['warning'] = "申请量超出部门平均水平"
        else:
            analysis['risk_level'] = 0.1
            analysis['warning'] = "符合部门申请模式"
        
        return analysis
    
    def _calculate_risk_score(self, risk_levels):
        """计算综合风险分数"""
        total_score = 0
        for risk_type, weight in self.risk_weights.items():
            risk_key = risk_type.replace('_risk', '')
            if risk_key in risk_levels:
                total_score += risk_levels[risk_key] * weight
        
        return min(1.0, total_score)
    
    def _generate_recommendation(self, risk_score, analyses):
        """生成审批建议"""
        if risk_score >= 0.8:
            recommendation = {
                'action': 'reject',
                'confidence': 0.9,
                'reason': '高风险申请，建议拒绝'
            }
        elif risk_score >= 0.6:
            recommendation = {
                'action': 'review',
                'confidence': 0.7,
                'reason': '中等风险申请，建议人工审核'
            }
        elif risk_score >= 0.3:
            recommendation = {
                'action': 'approve_with_conditions',
                'confidence': 0.8,
                'reason': '低风险申请，可有条件批准'
            }
        else:
            recommendation = {
                'action': 'approve',
                'confidence': 0.9,
                'reason': '低风险申请，建议批准'
            }
        
        # 添加具体建议
        suggestions = []
        for analysis_type, analysis in analyses.items():
            if analysis.get('warning') and analysis.get('risk_level', 0) > 0.5:
                suggestions.append(f"{analysis_type}: {analysis['warning']}")
        
        recommendation['suggestions'] = suggestions
        return recommendation
    
    def _get_risk_level_text(self, risk_score):
        """获取风险等级文本"""
        if risk_score >= 0.8:
            return "高风险"
        elif risk_score >= 0.6:
            return "中等风险"
        elif risk_score >= 0.3:
            return "低风险"
        else:
            return "极低风险"
    
    def _batch_analyze(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """批量分析申请"""
        request_ids = input_data.get('request_ids', [])
        
        try:
            results = []
            for request_id in request_ids:
                analysis = self._analyze_request({'request_id': request_id})
                if analysis['success']:
                    results.append({
                        'request_id': request_id,
                        'risk_score': analysis['data']['risk_score'],
                        'recommendation': analysis['data']['recommendation']
                    })
            
            # 按风险分数排序
            results.sort(key=lambda x: x['risk_score'], reverse=True)
            
            return AgentResponse(
                True,
                data=results,
                message=f"批量分析完成，共处理 {len(results)} 个申请",
                confidence=0.8
            ).to_dict()
            
        except Exception as e:
            return AgentResponse(False, message=f"批量分析失败: {e}").to_dict()
    
    def _risk_assessment(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """风险评估"""
        # 实现风险评估逻辑
        return self._analyze_request(input_data)
    
    def _approval_suggestion(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """审批建议"""
        # 实现审批建议逻辑
        analysis = self._analyze_request(input_data)
        if analysis['success']:
            return AgentResponse(
                True,
                data=analysis['data']['recommendation'],
                message="审批建议生成完成",
                confidence=analysis['data']['recommendation']['confidence']
            ).to_dict()
        return analysis
    
    def _fraud_detection(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """欺诈检测"""
        # 实现简单的欺诈检测逻辑
        user_id = input_data.get('user_id')
        
        try:
            # 检查异常模式
            recent_requests = self.request_dao.get_user_recent_requests(user_id, days=7)
            
            fraud_indicators = []
            risk_score = 0
            
            # 检查短期内大量申请
            if len(recent_requests) > 20:
                fraud_indicators.append("短期内申请次数异常")
                risk_score += 0.4
            
            # 检查申请时间模式
            request_hours = [r.get('request_date').hour for r in recent_requests if r.get('request_date')]
            if len(set(request_hours)) == 1 and len(request_hours) > 5:
                fraud_indicators.append("申请时间模式异常")
                risk_score += 0.3
            
            return AgentResponse(
                True,
                data={
                    'fraud_indicators': fraud_indicators,
                    'risk_score': risk_score,
                    'is_suspicious': risk_score > 0.5
                },
                message="欺诈检测完成",
                confidence=0.7
            ).to_dict()
            
        except Exception as e:
            return AgentResponse(False, message=f"欺诈检测失败: {e}").to_dict()
    
    def get_capabilities(self) -> List[str]:
        """获取智能体能力列表"""
        return [
            "申请分析",
            "批量分析",
            "风险评估",
            "审批建议",
            "欺诈检测"
        ]
