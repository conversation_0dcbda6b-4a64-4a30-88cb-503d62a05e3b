#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from services.material_service import MaterialService
from models.user import User

# 创建测试用户
employee_user = User(
    id=2,
    username='<PERSON><PERSON><PERSON>',
    real_name='张三',
    role='employee',
    department_id=1
)

material_service = MaterialService()

try:
    print("测试员工申请物资:")
    
    # 获取可申请的物资
    materials = material_service.get_material_list(employee_user)
    print(f"员工可见物资数量: {len(materials)}")
    
    if materials:
        # 选择第一个有剩余数量的物资
        available_material = None
        for material in materials:
            if material['remaining_quantity'] > 0:
                available_material = material
                break

        if available_material:
            print(f"\n选择物资: {available_material['name']}")
            print(f"剩余数量: {available_material['remaining_quantity']}")

            # 申请物资
            result = material_service.request_material(
                available_material['id'],
                1,
                "测试申请物资",
                employee_user
            )
            
            print(f"申请成功! 申请ID: {result.id}")
            print(f"申请状态: {result.status}")
            
        else:
            print("没有可申请的物资")
    
except Exception as e:
    print(f"错误: {e}")
    import traceback
    traceback.print_exc()
