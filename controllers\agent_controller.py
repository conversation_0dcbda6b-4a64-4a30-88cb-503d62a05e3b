#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能体控制器
处理智能体相关的Web请求
"""

from flask import Blueprint, request, jsonify, session, render_template, redirect, url_for
from agents.agent_coordinator import AgentCoordinator
from agents.request_assistant_agent import RequestAssistantAgent
from agents.approval_assistant_agent import ApprovalAssistantAgent
from agents.inventory_manager_agent import InventoryManagerAgent
from agents.report_analyst_agent import ReportAnalystAgent
from services.deepseek_service import deepseek_service
import json
import os

# 创建蓝图
agent_bp = Blueprint('agent', __name__, url_prefix='/agent')

# 初始化智能体
coordinator = AgentCoordinator()
request_assistant = RequestAssistantAgent()
approval_assistant = ApprovalAssistantAgent()
inventory_manager = InventoryManagerAgent()
report_analyst = ReportAnalystAgent()

@agent_bp.route('/dashboard')
def dashboard():
    """智能体控制台"""
    if 'user_id' not in session:
        return jsonify({'error': '请先登录'}), 401
    
    return render_template('agent_dashboard.html')

@agent_bp.route('/status')
def get_agent_status():
    """获取所有智能体状态"""
    if 'user_id' not in session:
        return jsonify({'error': '请先登录'}), 401
    
    try:
        result = coordinator.process({'action': 'agent_status'})
        return jsonify(result)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@agent_bp.route('/health')
def system_health():
    """系统健康检查"""
    if 'user_id' not in session:
        return jsonify({'error': '请先登录'}), 401
    
    try:
        result = coordinator.process({'action': 'monitor_system_health'})
        return jsonify(result)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@agent_bp.route('/request/assist', methods=['POST'])
def request_assist():
    """申请助手服务"""
    if 'user_id' not in session:
        return jsonify({'error': '请先登录'}), 401
    
    try:
        data = request.get_json()
        data['user_id'] = session['user_id']
        data['department_id'] = session.get('department_id')
        
        result = request_assistant.process(data)
        return jsonify(result)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@agent_bp.route('/approval/analyze', methods=['POST'])
def approval_analyze():
    """审批分析服务"""
    if 'user_id' not in session:
        return jsonify({'error': '请先登录'}), 401
    
    # 检查权限 - 只有管理员可以使用审批助手
    if session.get('role') != 'admin':
        return jsonify({'error': '权限不足'}), 403
    
    try:
        data = request.get_json()
        result = approval_assistant.process(data)
        return jsonify(result)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@agent_bp.route('/inventory/monitor')
def inventory_monitor():
    """库存监控"""
    if 'user_id' not in session:
        return jsonify({'error': '请先登录'}), 401
    
    try:
        data = request.args.to_dict()
        data['action'] = 'monitor_inventory'
        
        result = inventory_manager.process(data)
        return jsonify(result)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@agent_bp.route('/inventory/predict', methods=['POST'])
def inventory_predict():
    """需求预测"""
    if 'user_id' not in session:
        return jsonify({'error': '请先登录'}), 401
    
    try:
        data = request.get_json()
        data['action'] = 'predict_demand'
        
        result = inventory_manager.process(data)
        return jsonify(result)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@agent_bp.route('/inventory/reorder')
def inventory_reorder():
    """补货建议"""
    if 'user_id' not in session:
        return jsonify({'error': '请先登录'}), 401
    
    # 检查权限 - 只有管理员可以查看补货建议
    if session.get('role') != 'admin':
        return jsonify({'error': '权限不足'}), 403
    
    try:
        result = inventory_manager.process({'action': 'suggest_reorder'})
        return jsonify(result)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@agent_bp.route('/inventory/optimize', methods=['POST'])
def inventory_optimize():
    """分配优化"""
    if 'user_id' not in session:
        return jsonify({'error': '请先登录'}), 401
    
    # 检查权限 - 只有管理员可以使用分配优化
    if session.get('role') != 'admin':
        return jsonify({'error': '权限不足'}), 403
    
    try:
        data = request.get_json()
        data['action'] = 'optimize_allocation'
        
        result = inventory_manager.process(data)
        return jsonify(result)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@agent_bp.route('/report/insights')
def report_insights():
    """生成洞察报告"""
    if 'user_id' not in session:
        return jsonify({'error': '请先登录'}), 401
    
    try:
        data = request.args.to_dict()
        data['action'] = 'generate_insights'
        
        # 设置默认参数
        if 'period_days' not in data:
            data['period_days'] = 30
        else:
            data['period_days'] = int(data['period_days'])
        
        result = report_analyst.process(data)
        return jsonify(result)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@agent_bp.route('/report/trends')
def report_trends():
    """趋势分析"""
    if 'user_id' not in session:
        return jsonify({'error': '请先登录'}), 401
    
    try:
        data = request.args.to_dict()
        data['action'] = 'trend_analysis'
        
        # 设置默认参数
        if 'period_days' not in data:
            data['period_days'] = 90
        else:
            data['period_days'] = int(data['period_days'])
        
        if 'metric' not in data:
            data['metric'] = 'requests'
        
        result = report_analyst.process(data)
        return jsonify(result)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@agent_bp.route('/report/anomalies')
def report_anomalies():
    """异常检测"""
    if 'user_id' not in session:
        return jsonify({'error': '请先登录'}), 401
    
    try:
        result = report_analyst.process({'action': 'anomaly_detection'})
        return jsonify(result)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@agent_bp.route('/report/performance')
def report_performance():
    """性能分析"""
    if 'user_id' not in session:
        return jsonify({'error': '请先登录'}), 401
    
    try:
        result = report_analyst.process({'action': 'performance_analysis'})
        return jsonify(result)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@agent_bp.route('/report/comprehensive')
def comprehensive_report():
    """综合报告"""
    if 'user_id' not in session:
        return jsonify({'error': '请先登录'}), 401
    
    try:
        data = request.args.to_dict()
        data['action'] = 'generate_comprehensive_report'
        
        # 设置默认参数
        if 'period_days' not in data:
            data['period_days'] = 30
        else:
            data['period_days'] = int(data['period_days'])
        
        if 'report_type' not in data:
            data['report_type'] = 'full'
        
        result = coordinator.process(data)
        return jsonify(result)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@agent_bp.route('/workflow/request', methods=['POST'])
def workflow_request():
    """申请处理工作流程"""
    if 'user_id' not in session:
        return jsonify({'error': '请先登录'}), 401
    
    try:
        data = request.get_json()
        data['action'] = 'coordinate_request_process'
        
        result = coordinator.process(data)
        return jsonify(result)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@agent_bp.route('/workflow/optimize')
def workflow_optimize():
    """工作流程优化"""
    if 'user_id' not in session:
        return jsonify({'error': '请先登录'}), 401
    
    # 检查权限 - 只有管理员可以优化工作流程
    if session.get('role') != 'admin':
        return jsonify({'error': '权限不足'}), 403
    
    try:
        data = request.args.to_dict()
        data['action'] = 'optimize_workflow'
        
        if 'workflow_type' not in data:
            data['workflow_type'] = 'request_process'
        
        result = coordinator.process(data)
        return jsonify(result)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@agent_bp.route('/chat', methods=['POST'])
def agent_chat():
    """智能体对话接口"""
    if 'user_id' not in session:
        return jsonify({'error': '请先登录'}), 401
    
    try:
        data = request.get_json()
        agent_type = data.get('agent_type', 'coordinator')
        message = data.get('message', '')
        
        # 根据消息内容智能路由到合适的智能体
        if '申请' in message or '需求' in message:
            agent = request_assistant
        elif '审批' in message or '批准' in message:
            agent = approval_assistant
        elif '库存' in message or '补货' in message:
            agent = inventory_manager
        elif '报告' in message or '分析' in message:
            agent = report_analyst
        else:
            agent = coordinator
        
        # 简化的对话处理
        response = {
            'success': True,
            'data': {
                'agent_name': agent.name,
                'response': f"收到您的消息：{message}。我是{agent.name}，可以为您提供以下服务：{', '.join(agent.get_capabilities())}"
            },
            'message': '对话处理完成'
        }
        
        return jsonify(response)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@agent_bp.route('/chat', methods=['POST'])
def chat():
    """智能对话接口"""
    if 'user_id' not in session:
        return jsonify({'success': False, 'error': '请先登录'})

    try:
        data = request.get_json()
        message = data.get('message', '')
        context = data.get('context', {})

        if not message:
            return jsonify({'success': False, 'error': '消息不能为空'})

        # 添加用户信息到上下文
        context['user_id'] = session['user_id']

        # 使用DeepSeek服务进行对话
        result = deepseek_service.chat_with_user(message, context)

        # 如果AI服务失败，提供备用响应
        if not result.get('success'):
            fallback_response = generate_fallback_response(message)
            result = {
                'success': True,
                'data': {
                    'response': fallback_response,
                    'agent_type': 'fallback'
                }
            }

        return jsonify(result)

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

def generate_fallback_response(message):
    """生成备用响应"""
    message_lower = message.lower()

    if '申请' in message_lower or '需求' in message_lower:
        return "我是智能申请助手。我可以帮助您：\n1. 推荐合适的物资\n2. 优化申请理由\n3. 预测需求量\n请告诉我您需要什么类型的物资？"

    elif '审批' in message_lower or '批准' in message_lower:
        return "我是智能审批助手。我可以帮助您：\n1. 分析申请合理性\n2. 评估风险等级\n3. 提供审批建议\n请提供需要审批的申请信息。"

    elif '库存' in message_lower or '补货' in message_lower:
        return "我是智能库存管理助手。我可以帮助您：\n1. 检查库存状态\n2. 预测补货需求\n3. 优化分配策略\n请告诉我您想了解哪种物资的库存情况？"

    elif '报告' in message_lower or '分析' in message_lower:
        return "我是智能报表分析助手。我可以帮助您：\n1. 生成趋势报告\n2. 检测异常模式\n3. 分析成本效益\n请告诉我您需要什么类型的报告？"

    elif '你好' in message_lower or 'hello' in message_lower:
        return "您好！我是金融企业物资管理系统的智能助手。我可以帮助您：\n• 物资申请和推荐\n• 审批流程协助\n• 库存监控管理\n• 数据分析报告\n\n请告诉我您需要什么帮助？"

    elif '帮助' in message_lower or 'help' in message_lower:
        return "我可以为您提供以下服务：\n\n📋 申请助手：物资推荐、申请优化\n✅ 审批助手：合理性分析、风险评估\n📦 库存管理：状态监控、补货预测\n📊 报表分析：趋势分析、异常检测\n\n请选择您需要的服务类型，或直接描述您的需求。"

    else:
        return f"收到您的消息：{message}\n\n我是智能物资管理助手，目前AI服务暂时不可用，但我仍然可以为您提供基础服务。请告诉我您需要：\n• 物资申请帮助\n• 审批流程协助\n• 库存状态查询\n• 报表数据分析\n\n或者您可以前往API配置页面设置DeepSeek密钥以启用完整的AI功能。"

@agent_bp.route('/config')
def api_config():
    """API配置页面"""
    if 'user_id' not in session:
        return redirect(url_for('auth.login'))

    return render_template('api_config.html')

@agent_bp.route('/api_status')
def api_status():
    """检查API配置状态"""
    try:
        # 检查配置文件是否存在
        config_file = 'api_config.json'

        if os.path.exists(config_file):
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)

            # 简单测试API是否可用
            test_result = deepseek_service.chat_completion("测试", "你好")

            return jsonify({
                'success': True,
                'data': {
                    'configured': test_result.get('success', False),
                    'model': config.get('model', 'unknown')
                }
            })
        else:
            return jsonify({
                'success': True,
                'data': {
                    'configured': False,
                    'model': None
                }
            })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@agent_bp.route('/save_api_config', methods=['POST'])
def save_api_config():
    """保存API配置"""
    if 'user_id' not in session:
        return jsonify({'success': False, 'error': '请先登录'})

    try:
        data = request.get_json()
        api_key = data.get('api_key', '').strip()
        base_url = data.get('base_url', 'https://api.deepseek.com').strip()
        model = data.get('model', 'deepseek-chat').strip()

        if not api_key:
            return jsonify({'success': False, 'error': 'API密钥不能为空'})

        # 保存配置到文件
        config = {
            'api_key': api_key,
            'base_url': base_url,
            'model': model
        }

        with open('api_config.json', 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)

        # 更新DeepSeek服务配置
        deepseek_service.update_config(api_key, base_url, model)

        return jsonify({'success': True, 'message': 'API配置保存成功'})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@agent_bp.route('/test_api', methods=['POST'])
def test_api():
    """测试API连接"""
    try:
        data = request.get_json()
        api_key = data.get('api_key', '').strip()
        base_url = data.get('base_url', 'https://api.deepseek.com').strip()
        model = data.get('model', 'deepseek-chat').strip()

        if not api_key:
            return jsonify({'success': False, 'error': 'API密钥不能为空'})

        # 临时创建DeepSeek服务实例进行测试
        from services.deepseek_service import DeepSeekService
        test_service = DeepSeekService(api_key, base_url, model)

        # 发送测试请求
        result = test_service.chat_completion("你好，这是一个API连接测试", "请简单回复确认连接正常")

        if result.get('success'):
            return jsonify({
                'success': True,
                'data': {
                    'response': result.get('data', {}).get('response', '连接成功')
                }
            })
        else:
            return jsonify({'success': False, 'error': result.get('error', 'API测试失败')})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

# 快速操作路由
@agent_bp.route('/coordinator/comprehensive_report', methods=['POST'])
def comprehensive_report():
    """生成综合报告"""
    if 'user_id' not in session:
        return jsonify({'success': False, 'error': '请先登录'})

    try:
        coordinator = AgentCoordinator()
        result = coordinator.generate_comprehensive_report()

        return jsonify({
            'success': True,
            'data': {
                'title': '系统综合报告',
                'content': result.get('data', {}).get('report', '报告生成失败'),
                'timestamp': result.get('data', {}).get('timestamp', '')
            }
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@agent_bp.route('/inventory/monitor')
def monitor_inventory():
    """监控库存状态"""
    if 'user_id' not in session:
        return jsonify({'success': False, 'error': '请先登录'})

    try:
        inventory_agent = InventoryManagerAgent()
        result = inventory_agent.monitor_inventory()

        return jsonify({
            'success': True,
            'data': {
                'title': '库存监控报告',
                'content': result.get('data', {}).get('report', '监控失败'),
                'alerts': result.get('data', {}).get('alerts', [])
            }
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@agent_bp.route('/report/anomaly_detection')
def anomaly_detection():
    """异常检测"""
    if 'user_id' not in session:
        return jsonify({'success': False, 'error': '请先登录'})

    try:
        report_agent = ReportAnalystAgent()
        result = report_agent.detect_anomalies()

        return jsonify({
            'success': True,
            'data': {
                'title': '异常检测报告',
                'content': result.get('data', {}).get('report', '检测失败'),
                'anomalies': result.get('data', {}).get('anomalies', [])
            }
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@agent_bp.route('/coordinator/optimize_workflow', methods=['POST'])
def optimize_workflow():
    """优化工作流程"""
    if 'user_id' not in session:
        return jsonify({'success': False, 'error': '请先登录'})

    # 检查权限
    user_role = session.get('role', '')
    if user_role != 'admin':
        return jsonify({'success': False, 'error': '只有管理员可以执行工作流程优化'})

    try:
        coordinator = AgentCoordinator()
        result = coordinator.optimize_workflow()

        return jsonify({
            'success': True,
            'data': {
                'title': '工作流程优化报告',
                'content': result.get('data', {}).get('report', '优化失败'),
                'recommendations': result.get('data', {}).get('recommendations', [])
            }
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})
