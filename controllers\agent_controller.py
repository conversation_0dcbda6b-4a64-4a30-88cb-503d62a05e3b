#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能体控制器
处理智能体相关的Web请求
"""

from flask import Blueprint, request, jsonify, session, render_template
from agents.agent_coordinator import AgentCoordinator
from agents.request_assistant_agent import RequestAssistantAgent
from agents.approval_assistant_agent import ApprovalAssistantAgent
from agents.inventory_manager_agent import InventoryManagerAgent
from agents.report_analyst_agent import ReportAnalystAgent
from services.deepseek_service import deepseek_service
import json

# 创建蓝图
agent_bp = Blueprint('agent', __name__, url_prefix='/agent')

# 初始化智能体
coordinator = AgentCoordinator()
request_assistant = RequestAssistantAgent()
approval_assistant = ApprovalAssistantAgent()
inventory_manager = InventoryManagerAgent()
report_analyst = ReportAnalystAgent()

@agent_bp.route('/dashboard')
def dashboard():
    """智能体控制台"""
    if 'user_id' not in session:
        return jsonify({'error': '请先登录'}), 401
    
    return render_template('agent_dashboard.html')

@agent_bp.route('/status')
def get_agent_status():
    """获取所有智能体状态"""
    if 'user_id' not in session:
        return jsonify({'error': '请先登录'}), 401
    
    try:
        result = coordinator.process({'action': 'agent_status'})
        return jsonify(result)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@agent_bp.route('/health')
def system_health():
    """系统健康检查"""
    if 'user_id' not in session:
        return jsonify({'error': '请先登录'}), 401
    
    try:
        result = coordinator.process({'action': 'monitor_system_health'})
        return jsonify(result)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@agent_bp.route('/request/assist', methods=['POST'])
def request_assist():
    """申请助手服务"""
    if 'user_id' not in session:
        return jsonify({'error': '请先登录'}), 401
    
    try:
        data = request.get_json()
        data['user_id'] = session['user_id']
        data['department_id'] = session.get('department_id')
        
        result = request_assistant.process(data)
        return jsonify(result)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@agent_bp.route('/approval/analyze', methods=['POST'])
def approval_analyze():
    """审批分析服务"""
    if 'user_id' not in session:
        return jsonify({'error': '请先登录'}), 401
    
    # 检查权限 - 只有管理员可以使用审批助手
    if session.get('role') != 'admin':
        return jsonify({'error': '权限不足'}), 403
    
    try:
        data = request.get_json()
        result = approval_assistant.process(data)
        return jsonify(result)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@agent_bp.route('/inventory/monitor')
def inventory_monitor():
    """库存监控"""
    if 'user_id' not in session:
        return jsonify({'error': '请先登录'}), 401
    
    try:
        data = request.args.to_dict()
        data['action'] = 'monitor_inventory'
        
        result = inventory_manager.process(data)
        return jsonify(result)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@agent_bp.route('/inventory/predict', methods=['POST'])
def inventory_predict():
    """需求预测"""
    if 'user_id' not in session:
        return jsonify({'error': '请先登录'}), 401
    
    try:
        data = request.get_json()
        data['action'] = 'predict_demand'
        
        result = inventory_manager.process(data)
        return jsonify(result)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@agent_bp.route('/inventory/reorder')
def inventory_reorder():
    """补货建议"""
    if 'user_id' not in session:
        return jsonify({'error': '请先登录'}), 401
    
    # 检查权限 - 只有管理员可以查看补货建议
    if session.get('role') != 'admin':
        return jsonify({'error': '权限不足'}), 403
    
    try:
        result = inventory_manager.process({'action': 'suggest_reorder'})
        return jsonify(result)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@agent_bp.route('/inventory/optimize', methods=['POST'])
def inventory_optimize():
    """分配优化"""
    if 'user_id' not in session:
        return jsonify({'error': '请先登录'}), 401
    
    # 检查权限 - 只有管理员可以使用分配优化
    if session.get('role') != 'admin':
        return jsonify({'error': '权限不足'}), 403
    
    try:
        data = request.get_json()
        data['action'] = 'optimize_allocation'
        
        result = inventory_manager.process(data)
        return jsonify(result)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@agent_bp.route('/report/insights')
def report_insights():
    """生成洞察报告"""
    if 'user_id' not in session:
        return jsonify({'error': '请先登录'}), 401
    
    try:
        data = request.args.to_dict()
        data['action'] = 'generate_insights'
        
        # 设置默认参数
        if 'period_days' not in data:
            data['period_days'] = 30
        else:
            data['period_days'] = int(data['period_days'])
        
        result = report_analyst.process(data)
        return jsonify(result)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@agent_bp.route('/report/trends')
def report_trends():
    """趋势分析"""
    if 'user_id' not in session:
        return jsonify({'error': '请先登录'}), 401
    
    try:
        data = request.args.to_dict()
        data['action'] = 'trend_analysis'
        
        # 设置默认参数
        if 'period_days' not in data:
            data['period_days'] = 90
        else:
            data['period_days'] = int(data['period_days'])
        
        if 'metric' not in data:
            data['metric'] = 'requests'
        
        result = report_analyst.process(data)
        return jsonify(result)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@agent_bp.route('/report/anomalies')
def report_anomalies():
    """异常检测"""
    if 'user_id' not in session:
        return jsonify({'error': '请先登录'}), 401
    
    try:
        result = report_analyst.process({'action': 'anomaly_detection'})
        return jsonify(result)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@agent_bp.route('/report/performance')
def report_performance():
    """性能分析"""
    if 'user_id' not in session:
        return jsonify({'error': '请先登录'}), 401
    
    try:
        result = report_analyst.process({'action': 'performance_analysis'})
        return jsonify(result)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@agent_bp.route('/report/comprehensive')
def comprehensive_report():
    """综合报告"""
    if 'user_id' not in session:
        return jsonify({'error': '请先登录'}), 401
    
    try:
        data = request.args.to_dict()
        data['action'] = 'generate_comprehensive_report'
        
        # 设置默认参数
        if 'period_days' not in data:
            data['period_days'] = 30
        else:
            data['period_days'] = int(data['period_days'])
        
        if 'report_type' not in data:
            data['report_type'] = 'full'
        
        result = coordinator.process(data)
        return jsonify(result)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@agent_bp.route('/workflow/request', methods=['POST'])
def workflow_request():
    """申请处理工作流程"""
    if 'user_id' not in session:
        return jsonify({'error': '请先登录'}), 401
    
    try:
        data = request.get_json()
        data['action'] = 'coordinate_request_process'
        
        result = coordinator.process(data)
        return jsonify(result)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@agent_bp.route('/workflow/optimize')
def workflow_optimize():
    """工作流程优化"""
    if 'user_id' not in session:
        return jsonify({'error': '请先登录'}), 401
    
    # 检查权限 - 只有管理员可以优化工作流程
    if session.get('role') != 'admin':
        return jsonify({'error': '权限不足'}), 403
    
    try:
        data = request.args.to_dict()
        data['action'] = 'optimize_workflow'
        
        if 'workflow_type' not in data:
            data['workflow_type'] = 'request_process'
        
        result = coordinator.process(data)
        return jsonify(result)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@agent_bp.route('/chat', methods=['POST'])
def agent_chat():
    """智能体对话接口"""
    if 'user_id' not in session:
        return jsonify({'error': '请先登录'}), 401
    
    try:
        data = request.get_json()
        agent_type = data.get('agent_type', 'coordinator')
        message = data.get('message', '')
        
        # 根据消息内容智能路由到合适的智能体
        if '申请' in message or '需求' in message:
            agent = request_assistant
        elif '审批' in message or '批准' in message:
            agent = approval_assistant
        elif '库存' in message or '补货' in message:
            agent = inventory_manager
        elif '报告' in message or '分析' in message:
            agent = report_analyst
        else:
            agent = coordinator
        
        # 简化的对话处理
        response = {
            'success': True,
            'data': {
                'agent_name': agent.name,
                'response': f"收到您的消息：{message}。我是{agent.name}，可以为您提供以下服务：{', '.join(agent.get_capabilities())}"
            },
            'message': '对话处理完成'
        }
        
        return jsonify(response)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@agent_bp.route('/chat', methods=['POST'])
def chat():
    """智能对话接口"""
    if 'user_id' not in session:
        return jsonify({'success': False, 'error': '请先登录'})

    try:
        data = request.get_json()
        message = data.get('message', '')
        context = data.get('context', {})

        if not message:
            return jsonify({'success': False, 'error': '消息不能为空'})

        # 添加用户信息到上下文
        context['user_id'] = session['user_id']

        # 使用DeepSeek服务进行对话
        result = deepseek_service.chat_with_user(message, context)

        return jsonify(result)

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})
